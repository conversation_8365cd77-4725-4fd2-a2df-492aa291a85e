/**
 * Customers page JavaScript functionality
 */

$(document).ready(function() {
    console.log('Customers page initialized');
    
    // Initialize any UI components
    initializeCustomersPage();
});

function initializeCustomersPage() {
    // Initialize date pickers if needed
    $('.ui.calendar').calendar({
        type: 'date',
        formatter: {
            date: function (date, settings) {
                if (!date) return '';
                var day = date.getDate();
                var month = date.getMonth() + 1;
                var year = date.getFullYear();
                return month + '/' + day + '/' + year;
            }
        }
    });
    
    // Initialize dropdowns
    $('.ui.dropdown').dropdown();
    
    // Initialize modals
    $('.ui.modal').modal();
    
    // Initialize tooltips
    $('[data-tooltip]').popup();
    
    // Initialize search functionality
    initializeCustomerSearch();
    
    // Initialize table sorting
    initializeTableSorting();
}

function initializeCustomerSearch() {
    // Add search functionality for customer tables
    $('#customer-search').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();
        var table = $('.ui.table tbody');
        
        table.find('tr').each(function() {
            var row = $(this);
            var text = row.text().toLowerCase();
            
            if (text.indexOf(searchTerm) > -1) {
                row.show();
            } else {
                row.hide();
            }
        });
    });
}

function initializeTableSorting() {
    // Add basic table sorting functionality
    $('.ui.table th.sortable').on('click', function() {
        var column = $(this);
        var table = column.closest('table');
        var columnIndex = column.index();
        var isAscending = !column.hasClass('sorted-asc');
        
        // Remove sorting classes from all columns
        table.find('th').removeClass('sorted-asc sorted-desc');
        
        // Add appropriate sorting class
        if (isAscending) {
            column.addClass('sorted-asc');
        } else {
            column.addClass('sorted-desc');
        }
        
        // Sort the table rows
        var rows = table.find('tbody tr').get();
        rows.sort(function(a, b) {
            var aText = $(a).find('td').eq(columnIndex).text();
            var bText = $(b).find('td').eq(columnIndex).text();
            
            if (isAscending) {
                return aText.localeCompare(bText);
            } else {
                return bText.localeCompare(aText);
            }
        });
        
        // Reorder the rows in the DOM
        $.each(rows, function(index, row) {
            table.find('tbody').append(row);
        });
    });
}

// Export customer data functions
function exportCustomerData(type) {
    // TODO: Implement export functionality
    console.log('Exporting customer data as:', type);
}

// Customer action functions
function viewCustomer(customerId) {
    // TODO: Implement view customer functionality
    console.log('Viewing customer:', customerId);
}

function editCustomer(customerId) {
    // TODO: Implement edit customer functionality
    console.log('Editing customer:', customerId);
}

function deleteCustomer(customerId) {
    // TODO: Implement delete customer functionality
    console.log('Deleting customer:', customerId);
}
