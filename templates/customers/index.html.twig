{% extends "layout.html.twig" %}

{% block title %}{{ view.title }}{% endblock %}

{% block content %}
<div class="ui container">
    <div class="ui grid">
        <div class="sixteen wide column">
            <h1 class="ui header">
                <i class="users icon"></i>
                <div class="content">
                    {{ view.pageTitle|default('Customers') }}
                    {% if view.facility %}
                        <div class="sub header">{{ view.facility.getName() }}</div>
                    {% endif %}
                </div>
            </h1>
        </div>
    </div>

    <div class="ui grid">
        <div class="sixteen wide column">
            <!-- Navigation tabs -->
            <div class="ui pointing secondary menu">
                <a class="item {{ view.action == 'reservations' ? 'active' : '' }}" 
                   href="{{ path('customers_index', {action: 'reservations', fid: view.facilityId}) }}">
                    <i class="calendar check icon"></i>
                    Reservations
                </a>
                <a class="item {{ view.action == 'moveins' ? 'active' : '' }}" 
                   href="{{ path('customers_index', {action: 'moveins', fid: view.facilityId}) }}">
                    <i class="sign in icon"></i>
                    Move-Ins
                </a>
            </div>

            <!-- Content area -->
            <div class="ui segment">
                {% if view.action == 'reservations' %}
                    {% include 'customers/reservations.html.twig' %}
                {% elseif view.action == 'moveins' %}
                    {% include 'customers/moveins.html.twig' %}
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Initialize any JavaScript functionality for customers page
    $(document).ready(function() {
        console.log('Customers page loaded');
    });
</script>
{% endblock %}
