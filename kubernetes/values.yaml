env:
  BROWSERSTACK_USER: 'sparefoot_DYFcTyZ8MZr'
  BROWSERSTACK_KEY: '********************'
  DISPLAY_ERRORS_LOG: '1'

  # google key (temporarily until we get it in salt)
  GOOGLE_API_KEY: AIzaSyCE-r44Xokig5AZvcH-AthCnt8fheYBKlI

  PENDO_KEY: '50b33cea-aa30-42eb-5809-4a4cfce27a1d'
{% if sf_env == 'dev' %}
  SERVICES_BASE_URL: 'sparefoot.moreyard.com'
  SELENIUM_BASE_URL: 'https://my.sparefoot.moreyard.com'
  URL_BID_OPTIMIZER_SERVICE: 'https://bid-optimizer.sparefoot.moreyard.com'
  MEMCACHE_SERVER: dev-session-vpc.yojb1s.cfg.use1.cache.amazonaws.com:11211
{% elif sf_env == 'stage' %}
  SERVICES_BASE_URL: 'sparefoot.extrameter.com'
  SELENIUM_BASE_URL: 'https://my.sparefoot.extrameter.com'
  URL_BID_OPTIMIZER_SERVICE: 'https://bid-optimizer.sparefoot.extrameter.com'
  MEMCACHE_SERVER: stg-session-vpc.yojb1s.cfg.use1.cache.amazonaws.com:11211
{% elif sf_env == 'prod' %}
  SERVICES_BASE_URL: 'sparefoot.com'
  SELENIUM_BASE_URL: 'https://my.sparefoot.com'
  URL_BID_OPTIMIZER_SERVICE: 'https://bid-optimizer.sparefoot.com'
  MEMCACHE_SERVER: session-vpc.yojb1s.cfg.use1.cache.amazonaws.com:11211
{% endif %}
  # master database
  MYSQL_SPAREFOOT_HOST: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_HOST'|format(sf_env)) }}
  MYSQL_SPAREFOOT_DB: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_DB'|format(sf_env)) }}
  MYSQL_SPAREFOOT_USER: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_USER'|format(sf_env)) }}
  MYSQL_SPAREFOOT_PASS: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_PASS'|format(sf_env)) }}
  MYSQL_SPAREFOOT_PORT: '{{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_SPAREFOOT_PORT'|format(sf_env)) }}'
  # reporting database
  MYSQL_REPORTING_DSN: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_REPORTING_DSN'|format(sf_env)) }}
  MYSQL_REPORTING_DB: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_REPORTING_DB'|format(sf_env)) }}
  MYSQL_REPORTING_USER: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_REPORTING_USER'|format(sf_env)) }}
  MYSQL_REPORTING_PASS: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_REPORTING_PASS'|format(sf_env)) }}
  MYSQL_REPORTING_PORT: '{{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_REPORTING_PORT'|format(sf_env)) }}'
  # netsuite
  NETSUITE_CONSUMER_KEY: {{ salt['pillar.get']('netsuite-%s:NETSUITE_CONSUMER_KEY'|format(sf_env)) }}
  NETSUITE_CONSUMER_SECRET: {{ salt['pillar.get']('netsuite-%s:NETSUITE_CONSUMER_SECRET'|format(sf_env)) }}
  NETSUITE_TOKEN_ID: {{ salt['pillar.get']('netsuite-%s:NETSUITE_TOKEN_ID'|format(sf_env)) }}
  NETSUITE_TOKEN_SECRET: {{ salt['pillar.get']('netsuite-%s:NETSUITE_TOKEN_SECRET'|format(sf_env)) }}
  # salesforce service-cloud
  SALESFORCE_AUTH_BASE_URL: {{ salt['pillar.get']('salesforce-%s:SALESFORCE_AUTH_BASE_URL'|format(sf_env)) }}
  SALESFORCE_CONSUMER_KEY: {{ salt['pillar.get']('salesforce-%s:SALESFORCE_CONSUMER_KEY'|format(sf_env)) }}
  SALESFORCE_CONSUMER_SECRET: {{ salt['pillar.get']('salesforce-%s:SALESFORCE_CONSUMER_SECRET'|format(sf_env)) }}
  SALESFORCE_USERNAME: {{ salt['pillar.get']('salesforce-%s:SALESFORCE_USERNAME'|format(sf_env)) }}
  SALESFORCE_PASSWORD: {{ salt['pillar.get']('salesforce-%s:SALESFORCE_PASSWORD'|format(sf_env)) }}
  SALESFORCE_OWNER_ID: {{ salt['pillar.get']('salesforce-%s:SALESFORCE_OWNER_ID'|format(sf_env)) }}
  # analytics database
  ANALYTICS_DSN: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_ANALYTICS_DSN'|format(sf_env)) }}
  ANALYTICS_USERNAME: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_ANALYTICS_USER'|format(sf_env)) }}
  ANALYTICS_PASSWORD: {{ salt['pillar.get']('mysqlsparefoot-%s:MYSQL_ANALYTICS_PASS'|format(sf_env)) }}
  PHLOW_HOSTNAME: localhost
  APP_NAME: myfoot
  SOLR_HOST: solrmaster.{{ sf_env }}.svc.cluster.local # write only
  SOLR_PORT: '{{ salt['pillar.get']('inventoryservice-%s:SOLRPORT'|format(sf_env)) }}'
  SOLR_AUTH: {{ salt['pillar.get']('inventoryservice-%s:SOLR_AUTH'|format(sf_env)) }}
  SOLR_SCHEME: {{ salt['pillar.get']('inventoryservice-%s:SOLRSCHEME'|format(sf_env)) }}
  URL_AUTHORIZATION_SERVICE: 'http://authorization.{{ sf_env }}.svc.cluster.local'
  URL_LOCATION_SERVICE: 'http://locationservice.{{ sf_env }}.svc.cluster.local'
  URL_SEARCH_SERVICE: 'http://searchservice.{{ sf_env }}.svc.cluster.local'
  URL_BOOKING_SERVICE: 'http://bookingservice.{{ sf_env }}.svc.cluster.local'
  URL_CLIENT_API_SERVICE: 'http://clientapiservice.{{ sf_env }}.svc.cluster.local'
  URL_PHIDO: 'http://phido.{{ sf_env }}.svc.cluster.local'
  SEGMENT_KEY: "{{ salt['pillar.get']('myfoot-%s:SEGMENT_KEY'|format(sf_env)) }}"
  MYFOOT_USER: '{{ salt['pillar.get']('myfoot-%s:MYFOOT_USER'|format(sf_env)) }}'
  MYFOOT_PASSWORD: '{{ salt['pillar.get']('myfoot-%s:MYFOOT_PASSWORD'|format(sf_env)) }}'
  MYFOOT_TEST_EMAIL: '{{ salt['pillar.get']('myfoot-%s:MYFOOT_TEST_EMAIL'|format(sf_env)) }}'
  MYFOOT_TEST_PASSWORD: '{{ salt['pillar.get']('myfoot-%s:MYFOOT_TEST_PASSWORD'|format(sf_env)) }}'

backends:
  - service: authorization
  - service: locationservice
  - service: searchservice
  - service: bookingservice
  - service: clientapiservice
  - service: solrmaster

resources:
  requests:
    memory: '512Mi'
    cpu: '500m'
  limits:
    memory: '2G'
    cpu: '2000m'
envoy:
  ingressTimeout: '600s'

nodeSelector: 
  app2.sparefoot.com/group: "true"

hostnames:
{% if sf_env == 'prod' %}
  - my.sparefoot.com
  - mysparefoot.com
{% elif sf_env == 'stage' %}
  - my.sparefoot.extrameter.com
  - mysparefoot.extrameter.com
{% else %}
  - my.sparefoot.moreyard.com
  - mysparefoot.moreyard.com
{% endif %}
{% if sf_env == 'prod' %}
pdb:
  create: true
  minAvailable: 50%
{% endif %}