<?php

namespace Sparefoot\MyFootService\Service;

/**
 * GenesisService - Service for handling Genesis-related operations
 */
class GenesisService
{
    public function __construct()
    {
        // Initialize the service
    }

    /**
     * Get configuration from Genesis Config Factory.
     */
    public function configFactoryClassGetConfigs(): array
    {
        return \Genesis_Config_Factory::getConfigs();
    }

    public function isProduction(): bool
    {
        return \Genesis_Config_Server::isProduction();
    }

    public function isFeatureActive(string $feature): bool
    {
        return \Genesis_Service_Feature::isActive($feature);
    }
}
