<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class SitelinkController extends AbstractRestrictedController
{
    public const OID = '00DC0000000PXv8';

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sitelink/sales-lead
     * http://localhost:9019/sitelink/sales-lead
     */
    #[Route('/sitelink/sales-lead', name: 'sitelink_sales_lead', methods: ['POST'])]
    public function salesLeadAction(Request $request): JsonResponse
    {
        if ($request->isMethod('POST')) {
            $data = $request->request->all();

            $data['oid'] = self::OID;
            $data['retURL'] = 'http://sparefoot.force.com/thankyou';
            $data['lead_source'] = 'SpareFoot';
            $data['promotion__c'] = 'MyFoot-Jan15';
            $data['00NC0000005eubi'] = 'MyFoot-Jan15';
            // $data['debug'] = 1;
            // $data['debugEmail'] = '<EMAIL>';

            // curl submit the salesforce form
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://www.salesforce.com/servlet/servlet.WebToLead?encoding=UTF-8');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);

            $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            // saves to the actions table
            $logger = new \Genesis_Util_ActionLogger();
            $logger->logAction('sitelink_lead', 0, 0, $data['user_id']);

            if ($statusCode != 200) {
                $errText = 'Sitelink lead submit to Salesforce failed.';
                curl_close($ch);

                return new JsonResponse(['error' => $errText], $statusCode);
            } else {
                curl_close($ch);

                return new JsonResponse(['success' => true]);
            }
        } else {
            return new JsonResponse(['error' => 'Method not allowed.'], 405);
        }
    }
}
