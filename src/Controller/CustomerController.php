<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CustomerController extends AbstractRestrictedController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/customers
     * http://localhost:9019/customers
     */
    #[Route('/customers', name: 'customers_index', methods: ['GET'])]
    public function indexAction(Request $request): Response
    {
        return $this->redirectToRoute('customers_reservations');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/customers/inquiries
     * http://localhost:9019/customers/inquiries
     */
    #[Route('/customers/inquiries', name: 'customers_inquiries', methods: ['GET'])]
    public function inquiriesAction(Request $request): Response
    {
        // Check if user has manageable facilities
        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            return $this->redirectToRoute('features_add_first');
        }

        $this->initializeCustomerData($request);

        $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $inquiries = $this->fetchInquiries();

        return $this->render('customers/inquiries.html.twig', [
            'facility' => $facility,
            'inquiries' => $inquiries,
            'scripts' => ['facility/reservations'],
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/customers/tenants
     * http://localhost:9019/customers/tenants
     */
    #[Route('/customers/tenants', name: 'customers_tenants', methods: ['GET'])]
    public function tenantsAction(Request $request): Response
    {
        return $this->forward('reservations');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/customers/reservations
     * http://localhost:9019/customers/reservations
     */
    #[Route('/customers/reservations', name: 'customers_reservations', methods: ['GET'])]
    public function reservationsAction(Request $request): Response
    {
        // Check if user has manageable facilities
        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            return $this->redirectToRoute('features_add_first');
        }

        $this->initializeCustomerData($request);

        $facilityId = $this->getSession()->facilityId;
        $facility = \Genesis_Service_Facility::loadById($facilityId);
        $includeTenantConnectCalls = ($facility->getTenantConnect() == 1);
        $reservations = $this->fetchReservationsData($includeTenantConnectCalls);
        $bookingMeta = $this->fetchBookingMeta($reservations);

        return $this->render('customers/reservations.html.twig', [
            'facility' => $facility,
            'includeTenantConnectCalls' => $includeTenantConnectCalls,
            'reservations' => $reservations,
            'bookingMeta' => $bookingMeta,
            'view' => $this->view,
        ]);
    }

    private function initializeCustomerData(Request $request): void
    {
        $this->view->hasOnlineMoveInFmsSoftware = true; // Default to true, replace with actual service call if available

        $this->view->banner = [
            'showMoveInsBanner' => \Genesis_Service_Feature::isActive('myfoot.move_in_banner', []),
            'showMoveInOnlineBanner' => \Genesis_Service_Feature::isActive('myfoot.move_in_online_banner', []),
            'showNotificationBanner' => false, // Default to false, replace with actual validation if available
        ];
    }

    /**
     * Fetch and organize all of the data to populate the reservations screen.
     *
     * @return array
     */
    private function fetchReservationsData($includeTenantConnectCalls = false)
    {
        $startDate = $this->getTrueBeginDate();
        $endDate = $this->getTrueEndDate();

        $facilityId = $this->getSession()->facilityId;

        return \Genesis_Service_Transaction::loadByFacility($facilityId, $startDate, $endDate, $includeTenantConnectCalls, \Genesis_Db_Order::desc('timestamp'));
    }

    private function fetchBookingMeta($reservations)
    {
        $bookingMeta = [];
        foreach ($reservations as $reservation) {
            $confCode = $reservation['confirmation_code'];
            $billingPriceMeta = \Genesis_Service_BookingMeta::loadByConfirmationCodeAndKey(
                $confCode, \Genesis_Entity_BookingMeta::BILLING_PRICE_FOR_TOS_BILLING
            )->current();
            $discountPriceMeta = \Genesis_Service_BookingMeta::loadByConfirmationCodeAndKey(
                $confCode, \Genesis_Entity_BookingMeta::DISCOUNT_PRICE_FOR_TOS_BILLING
            )->current();
            $bookingMeta[$confCode] = [
                'billing_price' => $billingPriceMeta ? $billingPriceMeta->getValue() : null,
                'discount_price' => $discountPriceMeta ? $discountPriceMeta->getValue() : null,
            ];
        }

        return $bookingMeta;
    }

    private function fetchInquiries()
    {
        $startDate = $this->getTrueBeginDate();
        $endDate = $this->getTrueEndDate();

        $facilityId = $this->getSession()->facilityId;

        return \Genesis_Service_Inquiry::loadUniqueByFacilityIdAndDateRange($facilityId, $startDate, $endDate);
    }
}
