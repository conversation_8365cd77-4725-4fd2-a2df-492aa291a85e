<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Controller\AbstractView\GenericView;
use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Models\PendoData;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\UserCookie;
use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Service\UserRedirect;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Abstract Restricted Controller.
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 * Migrated to Symfony by Augment Agent
 */
abstract class AbstractRestrictedController extends AbstractCommonController
{
    /**
     * @var \Zend_Session_Namespace
     */
    protected $_session;

    private $_endDate;
    private $_beginDate;
    private $_trueBeginDate;
    private $_trueEndDate;
    private $_loggedUser = false;

    // Tab constants
    public const TAB_HOME = 'home';
    public const TAB_BILLING = 'billing';
    public const TAB_FACILITY = 'facilities';
    public const TAB_SETTINGS = 'settings';
    public const TAB_WIDGET = 'widget';
    public const TAB_HOSTEDWEBSITE = 'hostedwebsite';
    public const TAB_MOVE_INS = 'move-ins';
    public const TAB_CUSTOMERS = 'customers';
    public const TAB_REVIEWS = 'reviews';

    protected $view;

    /**
     * Migrated from final public function init()
     * This is called in ControllerInitSubscriber.
     */
    public function init(Request $request): mixed
    {
        if (empty($this->view)) {
            $this->view = new GenericView();
        }
        $this->setRequest($request);
        User::setRequest($request);
        // Call child controller initialization
        $this->_init();

        if ($this->getLoggedUser()) {
            $this->view->setLoggedUser($this->getLoggedUser());
            User::setUserAccess($this->getLoggedUser());
        }

        $this->initSiteTop();

        // START for sidebar
        $this->view->setActionName($this->getControllerActionName($request));
        $this->view->setRouteName($request->attributes->get('_route'));
        $this->view->setAccountId($this->getSession()->get('accountId'));
        $this->view->setFacilityId($this->getSession()->get('facilityId'));
        $this->initSidebar();
        // END for sidebar

        // Start SF script in layout.html.twig at bottom
        $this->sparefootScript();

        // Initialize authorization - this may return a redirect response
        // //this will set session->facilityId and session->accountId
        $authResponse = $this->_initAuthorization($request);

        if ($authResponse) {
            return $authResponse;
        }

        // Initialize date ranges and UI state
        $this->_initDateRange($request);
        $this->_initTrueDateRange($request);
        $this->_initUiState($request);

        $this->view->errorMessages = [];
        $this->view->successMessages = [];

        return null;
    }

    private function _clear(): void
    {
        $this->_loggedUser = null;
        $this->_session = null;
        $this->_beginDate = null;
        $this->_endDate = null;
        $this->_trueBeginDate = null;
        $this->_trueEndDate = null;
    }

    private function _forceTerms(Request $request): ?Response
    {
        // Rules only apply to these lesser account roles
        if ($this->getLoggedUser()->isMyFootGod()) {
            return null;
        }

        // Get controller name from route
        $route = $request->attributes->get('_route');
        $controllerName = $this->getControllerNameFromRoute($route);

        // Certain urls can work without terms, let the accounts controller pass
        // or else they can never add payment methods and pass this point
        if ($controllerName === 'accounts') {
            return null;
        }
        // Let the error controller fire
        if ($controllerName === 'error') {
            return null;
        }

        // Force agree to the pre-terms
        if (!\Genesis_Service_Account::termsAccepted($this->getLoggedUser()->getAccount())) {
            if (!$this->getLoggedUser()->isMyfootAdmin()) {
                return null; // tech debt. Fix this
                $message = '(terms incomplete) Please ask your account admin to sign in. '
                    .' Your account admins are: ';
                /**
                 * @var $adminUser \Genesis_Entity_User
                 */
                foreach ($this->getLoggedUser()->getAccount()->getAdmins() as $adminUser) {
                    $message .= $adminUser->getEmail().' ';
                }

                throw new \Exception($message);
            }
            if ($controllerName !== 'signup-end'
                && $request->get('action') !== 'terms'
            ) {
                return $this->redirect('/signup-end/terms');
            }
        }

        // Encourage to setup a billable entity
        if ($this->getLoggedUser()->getAccount()->getNumBillableEntities() === 0) {
            if ($this->getLoggedUser()->isMyfootAdmin()
                && $controllerName === 'login'
                && $request->get('action') === 'process-login') {
                return $this->redirect('/signup-end/billing');
            }
        }

        return null;
    }

    private function _initAuthorization(Request $request): ?Response
    {
        UserOauth::setRequest($request);
        // dump($this->getLoggedUser());
        // exit;
        // The user is not signed in. make them go sign in
        if (!$this->getLoggedUser() instanceof \Genesis_Entity_UserAccess) {
            /* Grab the URL and jam into a cookie so we can redirect on login */
            // Only set this if the URL is a GET, otherwise we might end up somewhere
            // and we won't have the parameters in the original POST, causing errors
            // (See mantis 1769)
            UserRedirect::setRedirect($request->getRequestUri());

            return $this->redirectToRoute('login_index');
        }

        $forceTermsResponse = $this->_forceTerms($request);
        if ($forceTermsResponse) {
            return $forceTermsResponse;
        }

        if ($this->getLoggedUser()->isMyFootGod()) {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), null, true);
        } else {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), 'client');
        }

        $genesisRequest = new \Genesis_Util_Request();
        \Genesis_Service_ActivityLog::logMyFootPageview($genesisRequest, $this->getLoggedUser());

        // Forward them to signup if they either do not have an account or do not have facilities yet
        if ($this->getLoggedUser() && (!$this->getLoggedUser()->getAccountId() || !$this->getLoggedUser()->getMyfootRole())) {
            $controllerName = $this->getControllerNameFromRoute($request->attributes->get('_route'));
            if ($controllerName !== 'signup') {
                if ($this->getLoggedUser()->getAccount() && $this->getLoggedUser()->getAccount()->getNumFacilities() == 0) {
                    return $this->redirect($this->generateUrl('features_type'));
                } elseif ($controllerName !== 'booking') {
                    return $this->redirect('/signup-start');
                }
            }
        }

        // We have an account ID now, but we might not have a valid Facility
        $this->view->accountId = $this->getSession()->get('accountId');
        if (empty($this->view->accountId)) {
            $this->view->accountId = $this->getLoggedUser()->getAccountId();
        }
        $this->view->facilityId = $this->getSession()->get('facilityId'); // used by the menu

        // The controller names a facility editor should have access to
        $this->view->sitesControllers = ['sites', 'settings'];

        // Set these so JS can also access
        $authBearerToken = UserOauth::getToken();
        $this->view->authBearerToken = '';
        $this->view->servicesBaseUrl = '';
        if ($authBearerToken) {
            // Unfortunately, we have to reference 'SF_ENV' here since servicesBaseUrl is used many times throughout client-side
            // JS. Also, we can't change getDomain() inside authorization service's code to locally use the FQDN instead of the
            // docker link. This is because we can't reference the FQDN for local authorization service inside MyFoot's container.
            $this->view->servicesBaseUrl = getenv('SERVICES_BASE_URL');
            $this->view->authBearerToken = $authBearerToken->__toString();
        }

        return null;
    }

    private function _initDateRange(Request $request): void
    {
        $dateRange = $request->get('date_range');
        if ($dateRange) {
            $this->getSession()->set('dateRange', $dateRange);
        } elseif (!$this->getSession()->get('dateRange')) {
            $this->getSession()->set('dateRange', 'week');
        }

        $this->view->dateRange = $this->getSession()->get('dateRange');

        $this->_endDate = date('Y-m-d H:i:s');

        switch ($this->getSession()->get('dateRange')) {
            case 'week':
                $this->_beginDate = date('Y-m-d', strtotime('-1 week'));
                break;
            case 'month':
                $this->_beginDate = date('Y-m-d', strtotime('-1 month'));
                break;
            case 'year':
                $this->_beginDate = date('Y-m-d', strtotime('-1 year'));
                break;
        }
    }

    private function _initTrueDateRange(Request $request): void
    {
        $trueDateRange = $request->get('true_date_range');
        if ($trueDateRange) {
            $this->getSession()->set('trueDateRange', $trueDateRange);
        } else {
            $this->getSession()->set('trueDateRange', date('M j, Y', strtotime('-1 month')).' - '.date('M j, Y'));
        }

        $this->view->trueDateRange = $this->getSession()->get('trueDateRange');
        $dates = explode(' - ', $this->view->trueDateRange);

        if (count($dates) == 2) {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $dates[1].' 23:59:59';
        } else {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $this->_trueBeginDate.' 23:59:59';
        }

        $this->view->trueBeginDate = $this->_trueBeginDate;
        $this->view->trueEndDate = $this->_trueEndDate;
    }

    private function _initUiState(Request $request): void
    {
        $this->view->selectedTab = $this->getTab();

        if ($request->get('welcome')) {
            $this->view->welcomeMessage = true;
        }
    }

    /**
     * @return \Genesis_Entity_UserAccess|false
     */
    protected function getLoggedUser()
    {
        $user = $this->getUser();
        $this->_loggedUser = $user->getGenesisUserAccess() ?? false;
        $this->view->loggedUser = $this->_loggedUser;

        return $this->_loggedUser;
        if (!$this->_loggedUser) {
            // The account id the user posed should be in session by now, if they are a god this will modify
            // user access and allow it
            try {
                $this->_loggedUser = User::getLoggedUser();
            } catch (\Exception $e) {
                // Logout user if there is no auth bearer token in cookie
                $message = 'Auth token is invalid or has expired.';
                if (!\Genesis_Config_Server::isProduction()) {
                    $message .= '<br/>Exception: '.$e->getMessage();
                }
                $this->getSession()->set('logoutMessage', $message);

                return $this->redirectToRoute('login_loign_logout');
            }
        }

        return $this->view->loggedUser = $this->_loggedUser;
    }

    /**
     * @return Zend_Session_Namespace
     */
    protected function getSession()
    {
        if (!$this->_session) {
            $this->view->session = $this->_session = User::getSession();
        }

        return $this->_session;
    }

    protected function getBeginDate(): ?string
    {
        return $this->_beginDate;
    }

    protected function getEndDate(): ?string
    {
        return $this->_endDate;
    }

    protected function getTrueBeginDate(): string
    {
        return date('Y-m-d H:i:s', strtotime($this->_trueBeginDate));
    }

    protected function getTrueEndDate(): string
    {
        return date('Y-m-d H:i:s', strtotime($this->_trueEndDate));
    }

    protected function getSideBarContent(): string
    {
        return '';
    }

    protected function getTab(): string
    {
        return '';
    }

    protected function dispatchError($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->errorMessages)) {
            $this->view->errorMessages[] = $messages;
        } else {
            $this->view->errorMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('error', $messages);
    }

    protected function dispatchSuccess($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->successMessages)) {
            $this->view->successMessages[] = $messages;
        } else {
            $this->view->successMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('success', $messages);
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     * In Symfony, this is typically handled by the Request object and form validation.
     */
    public function getParam(Request $request, string $paramName, $default = null)
    {
        $param = $request->get($paramName, $default);

        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }

    public function __destruct()
    {
        $this->_clear();
    }

    public function initSidebar(): void
    {
        $this->view->sideBar = new \stdClass();
        $fid = $this->getSession()->get('facilityId');
        $accountId = $this->getSession()->get('accountId');

        $facility = \Genesis_Service_Facility::loadById($fid);
        $account = \Genesis_Service_Account::loadById($accountId);
        $hasOmiCapableFms = User::hasAccessToOmiCapableFms();
        $this->view->sideBar->pendoData = null;
        $this->view->sideBar->includePendoScript = false;
        if (!empty($fid)) {
            $pendo = new PendoData(User::getLoggedUser(), $fid);
            $pendoData = $pendo->buildPendoObject();
            $this->view->sideBar->pendoData = $pendoData;
            $this->view->sideBar->includePendoScript = true;
            // echo $this->partial('pendoscript.phtml', ["pendoData" => $pendoData]);
        }

        // if we don't have a facility, we are not Full Service yet
        $isFullService = $facility ? $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET : 0;

        $featuresArray = ['features', 'features-units', 'features-listings', 'features-bid', 'features-bid-custom'];
        $moveinsArray = ['move-ins', 'contactless-move-ins', 'online-move-ins'];

        // set variables to be used in the View $this->view->sideBar
        $this->view->sideBar->fid = $fid;
        $this->view->sideBar->accountId = $accountId;
        $this->view->sideBar->facility = $facility;
        $this->view->sideBar->account = $account;
        $this->view->sideBar->hasOmiCapableFms = $hasOmiCapableFms;
        $this->view->sideBar->isFullService = $isFullService;
        $this->view->sideBar->featuresArray = $featuresArray;
        $this->view->sideBar->moveinsArray = $moveinsArray;

        // Initialize sidebar items
        $this->view->sideBar->openStatementHtml = $this->initOpenStatementItem();
        $this->view->sideBar->moveInsItemHtml = $this->initMoveInsItem();
        $this->view->sideBar->partnersItemHtml = $this->initPartnersItem();
        $this->view->sideBar->biddingItemHtml = $this->initBiddingItem();
        $this->view->sideBar->customersItemHtml = $this->initCustomersItem();
        $this->view->sideBar->featuresItemHtml = $this->initFeaturesItem();
        $this->view->sideBar->reviewsItemHtml = $this->initReviewsItem();
        $this->view->sideBar->statementsItemHtml = $this->initStatementsItem();
    }

    private function initOpenStatementItem(): string
    {
        $loggedUser = $this->getLoggedUser();
        $accountId = $this->getSession()->get('accountId');

        if ($loggedUser !== null && $loggedUser !== false && $loggedUser->canUseBilling()) {
            $openStatementBatch = \Genesis_Service_StatementBatch::loadByStatus();
            if ($openStatementBatch) {
                $openStatement = \Genesis_Service_Statement::loadByAccountIdAndStatementBatchId(
                    $loggedUser->getAccount()->getAccountId(),
                    $openStatementBatch->getId()
                );
                if ($openStatement) {
                    $urlAction = \Genesis_Service_Feature::isActive(
                        Features::NEW_STATEMENTS_PAGE,
                        ['account_id' => $accountId]
                    ) ? 'dispute' : 'view';

                    // Get route and action from the view
                    $route = $this->view->getRouteName() ?? '';
                    $action = $this->view->getActionName() ?? '';

                    return sprintf(
                        '<a class="item %s" data-page="statements" data-segment-category="sidebar" data-segment-label="statements" id="menu-statements" href="%s">
                            <i class="edit icon"></i> Reconcile %s
                        </a>',
                        ($route === 'statement' && $action === $urlAction) ? 'active yellow' : '',
                        $this->generateUrl('statement', ['action' => $urlAction, 'id' => $openStatement->getId()]).'?account_id='.$accountId,
                        ($urlAction === 'dispute') ? '<span style="font-size:10px; float:inherit;" class="ui label green">BETA</span>' : ''
                    );
                }
            }
        }

        return '';
    }

    private function initMoveInsItem(): string
    {
        $hasOmiCapableFms = $this->view->sideBar->hasOmiCapableFms;
        $moveinsArray = $this->view->sideBar->moveinsArray;

        if (\Genesis_Service_Feature::isActive('AccountMgmt_Models_Features::CONTACTLESS_MOVEIN_BADGING', [])
            || $hasOmiCapableFms) {
            // Get route from the view
            $route = $this->view->getRouteName() ?? '';

            return sprintf(
                '<a class="item %s" data-page="moveins" data-segment-category="sidebar" data-segment-label="moveins" id="menu-moveins" href="%s">
                    <img src="/images/ic_badge.svg" class="icon-badges">
                    <span>Move-Ins</span>
                </a>',
                in_array($route, $moveinsArray) ? 'active yellow' : '',
                $this->generateUrl('contactless-move-ins', ['action' => 'contactless'])
            );
        }

        return '';
    }

    private function initPartnersItem(): string
    {
        $loggedUser = $this->getLoggedUser();

        if ($loggedUser && $loggedUser->getAccount() && $loggedUser->getAccount()->getCpa()) {
            // Get route from the view
            $route = $this->view->getRouteName() ?? '';

            return sprintf(
                '<a class="item %s" data-page="partners" data-segment-category="sidebar" data-segment-label="partners" id="menu-partners" href="%s">
                    <img src="/images/partners_icon.svg" class="icon-badges">
                    <span>Partners</span>
                </a>',
                $route === 'partners' ? 'active yellow' : '',
                $this->generateUrl('partners')
            );
        }

        return '';
    }

    private function initBiddingItem(): string
    {
        $loggedUser = $this->getLoggedUser();
        $fid = $this->getSession()->get('facilityId');
        $featuresArray = $this->view->sideBar->featuresArray;

        if ($loggedUser && $loggedUser->getAccount() && $loggedUser->getAccount()->getCpa()) {
            // Get route and action from the view
            $route = $this->view->getRouteName() ?? '';
            $action = $this->view->getActionName() ?? '';

            if (\Genesis_Service_Feature::isActive('AccountMgmt_Models_Features::BID_OPTIMIZER', ['account_id' => $loggedUser->getAccountId()])) {
                // Demand optimizer version
                return sprintf(
                    '<a class="item %s" data-page="bidding" data-segment-category="sidebar" data-segment-label="bidding" id="menu-bidding" href="%s">
                        <i class="chart line icon"></i> Bidding
                    </a>',
                    $route === 'features-demand-optimizer' ? 'active yellow' : '',
                    $this->generateUrl('features', ['action' => 'demandoptimizer']).'?fid='.$fid
                );
            } else {
                // Regular bidding version
                $biddingHtml = sprintf(
                    '<a class="item %s" data-page="bidding" data-segment-category="sidebar" data-segment-label="bidding" id="menu-bidding" href="%s">
                        <i class="chart line icon"></i> Bidding
                    </a>',
                    (in_array($route, ['bidding', 'features-bid', 'features-bid-custom']) || $action === 'bulkbid') ? 'active yellow' : '',
                    $this->generateUrl('features', ['action' => 'bid']).'?fid='.$fid
                );

                // Update featuresArray to remove bid-related features
                $this->view->sideBar->featuresArray = array_diff($featuresArray, ['features-bid', 'features-bid-custom']);

                return $biddingHtml;
            }
        }

        return '';
    }

    private function initCustomersItem(): string
    {
        $fid = $this->getSession()->get('facilityId');

        // Get route from the view
        $route = $this->view->getRouteName() ?? '';

        return sprintf(
            '<a class="item %s" data-page="customers" data-segment-category="sidebar" data-segment-label="customers" id="menu-customers" href="%s">
                <i class="users icon"></i> Customers
            </a>',
            $route === 'customers_index' ? 'active yellow' : '',
            $this->generateUrl('customers_index', ['action' => 'reservations']).'?fid='.$fid
        );
    }

    private function initFeaturesItem(): string
    {
        $fid = $this->getSession()->get('facilityId');
        $featuresArray = $this->view->sideBar->featuresArray;

        // Get route and action from the view
        $route = $this->view->getRouteName() ?? '';
        $action = $this->view->getActionName() ?? '';

        return sprintf(
            '<a class="item %s" data-page="features" data-segment-category="sidebar" data-segment-label="features" id="menu-features" href="%s">
                <i class="building outline icon"></i> Features
            </a>',
            (in_array($route, $featuresArray) && $action !== 'bulkbid') ? 'active yellow' : '',
            $this->generateUrl('features_index', ['action' => 'units']).'?fid='.$fid
        );
    }

    private function initReviewsItem(): string
    {
        $fid = $this->getSession()->get('facilityId');

        // Get route from the view
        $route = $this->view->getRouteName() ?? '';

        return sprintf(
            '<a class="item %s" data-page="reviews" data-segment-category="sidebar" data-segment-label="reviews" id="menu-reviews" href="%s">
                <i class="star icon"></i> Reviews
            </a>',
            in_array($route, ['reviews_index', 'reviews-one', 'reviews-request']) ? 'active yellow' : '',
            $this->generateUrl('reviews_index', ['action' => 'index']).'?fid='.$fid
        );
    }

    private function initStatementsItem(): string
    {
        $loggedUser = $this->getLoggedUser();
        $accountId = $this->getSession()->get('accountId');

        // If billing is enabled
        if (isset($loggedUser) && $loggedUser && $loggedUser->canUseBilling()) {
            // Get route and action from the view
            $route = $this->view->getRouteName() ?? '';
            $action = $this->view->getActionName() ?? '';

            return sprintf(
                '<a class="item %s" data-page="statements" data-segment-category="sidebar" data-segment-label="statements" id="menu-statements" href="%s">
                    <i class="list icon"></i> Statements
                </a>',
                ($route === 'statement_index' && !in_array($action, ['dispute', 'view'])) ? 'active yellow' : '',
                $this->generateUrl('statement_index', ['action' => 'list']).'?account_id='.$accountId
            );
        }

        return '';
    }

    private function initSiteTop(): void
    {
        $this->view->siteTop = new \stdClass();
        $this->view->siteTop->haveLoggedUser = User::getLoggedUser();
        $this->view->siteTop->segmentScript = '';

        // Segment code only if key is set
        $segment_key = getenv('SEGMENT_KEY');
        if ($segment_key) {
            $loggedUser = $this->getLoggedUser();
            $segmentScript = '<script>
            !function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on"];analytics.factory=function(t){return function(){var e=Array.prototype.slice.call(arguments);e.unshift(t);analytics.push(e);return analytics}};for(var t=0;t<analytics.methods.length;t++){var e=analytics.methods[t];analytics[e]=analytics.factory(e)}analytics.load=function(t,e){var n=document.createElement("script");n.type="text/javascript";n.async=!0;n.src=("https:"===document.location.protocol?"https://":"http://")+"cdn.segment.com/analytics.js/v1/"+t+"/analytics.min.js";var o=document.getElementsByTagName("script")[0];o.parentNode.insertBefore(n,o);analytics._loadOptions=e};analytics.SNIPPET_VERSION="4.1.0";
                analytics.load("'.$segment_key.'");
            }}();';

            if ($loggedUser) {
                $facility = \Genesis_Service_Facility::loadById(UserCookie::get(UserCookie::ACTIVE_FACILITY_ID));
                $integrationTypeScript = '';
                if ($facility) {
                    $integrationTypeScript = ',
                integration_type: '.json_encode($facility->getCorporation()->getSource()->getSource());
                }

                $segmentScript .= '
            analytics.identify("'.$loggedUser->getId().'", {
                name: '.json_encode($loggedUser->getFirstName().' '.$loggedUser->getLastName()).',
                email: "'.$loggedUser->getEmail().'",
                sf_account_id: "'.$loggedUser->getAccount()->getSfAccountId().'",
                account_name: '.json_encode($loggedUser->getAccount()->getName()).',
                bid_type: "'.$loggedUser->getAccount()->getBidType().'"'.$integrationTypeScript.'
            });';
            }

            $segmentScript .= '
            analytics.page();
        </script>';

            $this->view->siteTop->segmentScript = $segmentScript;
        }
    }

    private function sparefootScript(): void
    {
        $loggedUser = $this->getLoggedUser();
        
        if (!$loggedUser) {
            $this->view->sparefootScript = '';
            return;
        }

        // Check if the feature is active
        $unitDeleteFeatureActive = User::isFeatureActive(Features::UNIT_DELETE);

        $httpHost = $this->getRequest()->getHost();
        
        // User fields
        $userFields = ['email', 'firstName', 'id', 'lastName', 'phone', 'pictureExt', 'username'];
        $userData = $loggedUser->toArray($userFields);
        
        // Account fields
        $accountFields = ['accountId', 'bidType', 'cpa', 'infoString', 'integrationsString', 'locationId', 'name', 'numFacilities', 'phone', 'sfAccountId', 'status'];
        $accountData = $loggedUser->getAccount()->toArray($accountFields);

        $script = '
        var CONFIG = { featureFlags: {} };
        // Feature Flags
        CONFIG.featureFlags[\''.htmlspecialchars(Features::UNIT_DELETE, ENT_QUOTES, 'UTF-8').'\'] = '.($unitDeleteFeatureActive ? 'true' : 'false').';

        SF.tools.setReadOnly(CONFIG, {
            appUrl: \'//'.htmlspecialchars($httpHost, ENT_QUOTES, 'UTF-8').'\',
            cdnUrl: \'//'.htmlspecialchars($httpHost, ENT_QUOTES, 'UTF-8').'\',
            env: location.hostname.match(\'sparefoot.com\') ? \'live\' : location.hostname.match(\'localhost\') ? \'dev\' : \'staging\'
        });

        var USER = {};
        SF.tools.setReadOnly(USER, '.json_encode($userData).');
        
        var ACCOUNT = {};
        SF.tools.setReadOnly(ACCOUNT, '.json_encode($accountData).');
        ';

        $this->view->sparefootScript = $script;
    }
}
