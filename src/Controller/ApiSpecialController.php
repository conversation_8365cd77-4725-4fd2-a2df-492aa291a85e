<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\Unit;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiSpecialController extends ApiBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/special
     * http://localhost:9019/api/special
     */
    #[Route('/api/special{slash}', name: 'api_special_index', requirements: ['slash' => '/?'], defaults: ['slash' => ''], methods: ['GET'])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $defaultSpecials = Unit::getDefaultSpecials();
        $items = [];

        foreach ($defaultSpecials as $special) {
            /** @var \Genesis_Entity_Special $special */
            $item = [
                'id' => $special->getId(),
                'string' => $special->getString(),
                'special_type' => $special->getType(),
                'percent_off' => $special->getPercentOff(),
                'dollar_off' => $special->getDollarOff(),
                'dollar_override' => $special->getDollarOverride(),
            ];
            if ($freeItem = $special->getFreeItem()) {
                $item['free_item_id'] = $freeItem->getId();
                $item['free_item_name'] = $freeItem->getName();
            }

            if ($t = $special->getRequiresPrepaidMonths()) {
                $item['requires_prepaid_months'] = $t;
            }

            if ($t = $special->getRequiresMinimumLeaseMonths()) {
                $item['requires_minimum_lease_months'] = $t;
            }

            if ($t = $special->getRequiresMinimumLeaseMonths()) {
                $item['months'] = $t;
            }

            $items[] = $item;
        }
        $result = [
            'count' => count($items),
            'data' => $items,
            'meta' => [],
        ];

        return $this->json($result);
    }
}
