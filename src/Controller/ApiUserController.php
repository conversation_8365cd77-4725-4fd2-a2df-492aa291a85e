<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Constants;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiUserController extends ApiBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/user/123
     * http://localhost:9019/api/user/123
     */
    #[Route('/api/user/{user_id}{slash}', name: 'api_user_show', requirements: ['slash' => '/?', 'user_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET', 'PUT', 'DELETE'])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        // path: /api/users/{ user_id }
        $userId = $request->get('user_id');
        if ($request->isMethod('GET') || $request->isMethod('PUT')) {
            $user = $this->validateAndGetUser($userId);
        }

        if ($request->isMethod('DELETE')) {
            $user = $this->validateAndGetUser($userId, Constants::ACTION_DELETE);
        }

        $json_body = $request->getContent();

        // GET
        if ($request->isMethod('GET')) {
            return $this->json(['data' => User::toArray($user)]);
        }

        // PUT
        if ($request->isMethod('PUT')) {
            User::updateFromJson($user, $json_body);

            return $this->json(['data' => User::toArray($user)]);
        }

        // DELETE
        if ($request->isMethod('DELETE')) {
            try {
                User::deleteUser($user);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }

            return $this->sendOKEmptyResponse();
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/user/me
     * http://localhost:9019/api/user/me
     */
    #[Route('/api/user/me{slash}', name: 'api_user_me', requirements: ['slash' => '/?'], defaults: ['slash' => ''], methods: ['GET'])]
    public function meAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $user = User::getLoggedUser();

        return $this->json(['data' => User::toArray($user)]);
    }
}
