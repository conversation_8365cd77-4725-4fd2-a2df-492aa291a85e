<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Facility Controller.
 *
 * @copyright 2025 SpareFoot Inc
 * <AUTHOR> Copilot
 */
class FacilityController extends AbstractRestrictedController
{
    /**
     * Initialize controller.
     */
    protected function _init(): void
    {
        // Facility-specific initialization can go here
    }

    /**
     * List Action - corresponds to the account overview route.
     */
    #[Route('/overview', name: 'account-overview', methods: ['GET'])]
    public function listAction(Request $request): Response
    {
        $this->view->title = 'Overview';
        return $this->render('facility/list.html.twig', [
            'view' => $this->view ?? new \stdClass(),
        ]);
    }

    /**
     * Get the current tab for navigation.
     */
    protected function getTab(): string
    {
        return parent::TAB_HOME;
    }
}
