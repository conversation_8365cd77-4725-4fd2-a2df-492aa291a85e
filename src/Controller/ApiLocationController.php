<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\Phlow;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiLocationController extends ApiBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * expects POST'ed JSON:
     * { zipCodes: [78701, ...] }
     *
     * passes through to location service
     *
     * POST because request uri too long with large zip set
     * /polygon returns combined shape, which is what we want
     * /polygons returns error message about which zips failed
     *
     * Sample:
     * https://myfoot.sparefoot.com/api/location/polygon
     * http://localhost:9019/api/location/polygon
     */
    #[Route('/api/location/polygon{slash}', name: 'api_location_polygon', requirements: ['slash' => '/?'], defaults: ['slash' => ''], methods: ['POST'])]
    public function polygonAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $input = json_decode(file_get_contents('php://input'));

        return $this->json(self::getPolygonDataForZipCodes($input->zipCodes));
    }

    /**
     * Get the combined polygon for many zips.
     * Get invalid zipcodes that may have been sent.
     *
     * @param array $zips [78701, ...]
     *
     * @return array [ 'polygon' => string, 'invalidZips' => array]
     *
     * @throws Exception
     */
    public static function getPolygonDataForZipCodes($zips)
    {
        $guzzle = new \GuzzleHttp\Client();
        $combinedUrl = getenv('URL_LOCATION_SERVICE').'/polygon?token=iamsparefoot';
        try {
            $response = $guzzle->post($combinedUrl, ['json' => [
                'zipCodes' => $zips,
                'timeout' => 60 * 3,
            ]]);
        } catch (\Exception $e) {
            // Record exception in Phlow
            Phlow::getClient()->increment(
                'get_polygon_exception',
                Phlow::getClient()->arrayToCsv([
                    'service' => 'location',
                    'path' => 'polygon',
                ])
            );
            throw $e;
        }
        $respBody = $response->getBody();

        return [
            'polygon' => "$respBody",
        ];
    }
}
