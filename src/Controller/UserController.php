<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class UserController extends AbstractRestrictedController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/about
     * http://localhost:9019/user/about
     */
    #[Route('/user/about', name: 'user_about', methods: ['GET'])]
    public function aboutAction(Request $request): Response
    {
        $account = $this->getLoggedUser()->getAccount();

        return $this->render('user/about.html.twig', [
            'account' => $account,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/users
     * http://localhost:9019/user/users
     */
    #[Route('/user/users', name: 'user_users', methods: ['GET'])]
    public function usersAction(Request $request): Response
    {
        $user = $this->getLoggedUser();
        $role = $user->getMyfootRole();
        if (!in_array($role, [
            \Genesis_Entity_UserAccess::ROLE_GOD,
            \Genesis_Entity_UserAccess::ROLE_ADMIN])) {
            return $this->redirectToRoute('settings');
        }

        $account = $user->getAccount();

        $currentPage = 1;
        $resultsPerPage = 50;

        if ($request->query->get('p')) {
            $currentPage = $request->query->get('p');
        }

        $facilities = \Genesis_Service_Facility::loadByAccountId(
            $account->getId(),
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('approved', 1),
                \Genesis_Db_Restriction::equal('published', 1)
            )
        )->toArray();

        $usersArray = [];

        // calculate number of facilities accessed for each user
        foreach (\Genesis_Service_UserAccess::load(\Genesis_Db_Restriction::equal('accountId', $account->getId())) as $userAccess) {
            /**
             * @var $userAccess \Genesis_Entity_UserAccess
             */
            $manageableFacIds = $userAccess->getManageableFacilityIds(null, false);
            $manageableFacIds = is_array($manageableFacIds) ? $manageableFacIds : [];
            $numFacilities = count($facilities) === count($manageableFacIds) ? 'All' : count($manageableFacIds);

            $usersArray[$userAccess->getId()]['id'] = $userAccess->getId();
            $usersArray[$userAccess->getId()]['email'] = $userAccess->getEmail();
            $usersArray[$userAccess->getId()]['first_name'] = $userAccess->getFirstName();
            $usersArray[$userAccess->getId()]['last_name'] = $userAccess->getLastName();
            $usersArray[$userAccess->getId()]['phone'] = $userAccess->getPhone();
            $usersArray[$userAccess->getId()]['myfootRole'] = $userAccess->getMyfootRole();
            $usersArray[$userAccess->getId()]['getsEmails'] = $userAccess->getGetsEmails();
            $usersArray[$userAccess->getId()]['getsStatements'] = $userAccess->getGetsStatements();
            $usersArray[$userAccess->getId()]['getsInquiries'] = $userAccess->getGetsInquiries();
            $usersArray[$userAccess->getId()]['accountId'] = $userAccess->getAccountId();
            $usersArray[$userAccess->getId()]['numFacilities'] = $numFacilities;
            $usersArray[$userAccess->getId()]['allFacs'] = $userAccess->getAllFacilities();
            $usersArray[$userAccess->getId()]['managableFacs'] = $manageableFacIds;
        }

        // paginate users list if needed
        $usersIterator = new \Genesis_Util_PaginatedIterator($usersArray, $currentPage, $resultsPerPage);

        return $this->render('user/users.html.twig', [
            'user' => $user,
            'account' => $account,
            'facilities' => $facilities,
            'totalPages' => $usersIterator->getNumPages(),
            'currentPage' => $currentPage,
            'users' => $usersIterator,
            'scripts' => ['user/users'],
            'title' => 'Users - Settings',
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/isadmin
     * http://localhost:9019/user/isadmin
     */
    #[Route('/user/isadmin', name: 'user_isadmin', methods: ['POST'])]
    public function isadminAction(Request $request): Response
    {
        // Make sure the user attempting privilege escalation is an admin
        // This feature is used with a js call to toggle admin permissions in user management
        $user = $this->getLoggedUser();
        if ($user && $user->getId()) {
            $role = $user->getMyfootRole();

            if ($role && !in_array($role, [\Genesis_Entity_UserAccess::ROLE_GOD, \Genesis_Entity_UserAccess::ROLE_ADMIN])) {
                return new Response("Error: You must be an administrator to change user roles. \n");
            }

            $userId = $request->request->get('uid');
            $isAdmin = $request->request->get('isAdmin');

            try {
                $ua = \Genesis_Service_UserAccess::loadById($userId);

                if (!$ua->isMyFootGod() && $ua->getAccountId() != $user->getAccountId()) {
                    return new Response("Error: You are not allowed to perform this action \n");
                }

                $ua->setMyfootAdminStatus($isAdmin);

                \Genesis_Service_User::save($ua);
                $ua = \Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());

                if ($ua->getAllFacilities()) {
                    return new Response('All');
                } else {
                    return new Response((string) count($ua->getManagableFacilities()->toArray()));
                }
            } catch (\Exception $e) {
                return new Response('Error: '.$e->getMessage()."\n");
            }
        }

        return new Response("Error: Unauthorized\n");
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/geopageonly
     * http://localhost:9019/user/geopageonly
     */
    #[Route('/user/geopageonly', name: 'user_geopageonly', methods: ['POST'])]
    public function geopageonlyAction(Request $request): Response
    {
        $userId = $request->request->get('uid');
        $geopageOnly = $request->request->get('geopageOnly');

        try {
            $ua = \Genesis_Service_UserAccess::loadById($userId);

            if ($geopageOnly) {
                $ua->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_LIMITED);
                $ua->setGetsStatements(0);
            } else {
                $ua->setMyfootRole(null);
            }

            $ua = \Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());

            if ($ua->getAllFacilities()) {
                return new Response('All');
            } else {
                return new Response((string) count($ua->getManagableFacilities()->toArray()));
            }
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n");
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/getsemails
     * http://localhost:9019/user/getsemails
     */
    #[Route('/user/getsemails', name: 'user_getsemails', methods: ['POST'])]
    public function getsemailsAction(Request $request): Response
    {
        try {
            $ua = \Genesis_Service_UserAccess::loadById($request->request->get('uid'));
            switch ($request->request->get('type')) {
                case 'reservations':
                    $ua->setGetsEmails($request->request->get('value'));
                    break;
                case 'inquiries':
                    $ua->setGetsInquiries($request->request->get('value'));
                    break;
                case 'statements':
                    if ($ua->isMyfootAdmin() && !$request->request->get('value')) {
                        throw new \Exception('Admins must get statements.');
                    }
                    $ua->setGetsStatements($request->request->get('value'));

                    // must have access to myfoot for this
                    if (!$ua->getMyfootRole()) {
                        $ua->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_LIMITED);
                    }
                    break;
                default:
                    throw new \Exception('incorrect type parameter');
            }
            \Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n");
        }

        return new Response('Success');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/accessmyfoot
     * http://localhost:9019/user/accessmyfoot
     */
    #[Route('/user/accessmyfoot', name: 'user_accessmyfoot', methods: ['POST'])]
    public function accessmyfootAction(Request $request): Response
    {
        $userId = $request->request->get('uid');

        $checkPassword = $request->request->has('pass');
        $accessMyfoot = $request->request->get('accessMyfoot');
        $pass = $request->request->get('pass', false);
        $passconfirm = $request->request->get('passconfirm', false);
        $notify = $request->request->get('notify');
        $passwordWasSet = false;

        try {
            $ua = \Genesis_Service_UserAccess::loadById($userId);
            $user = $this->getLoggedUser();

            if ($ua === null) {
                throw new \Exception('a valid uid (myfoot userId) is required');
            }
            // toggle myfoot admin access
            if ($ua->getAccountId() == $user->getAccountId() || $ua->isMyFootGod()) {
                if ($accessMyfoot) {
                    // if they don't have myfoot access, then allow them
                    if (!$ua->getMyfootRole()) {
                        $ua->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_LIMITED);
                    }
                } else {
                    // cannot take away myfoot access from admins
                    if ($ua->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_ADMIN) {
                        throw new \Exception('You cannot take away MySpareFoot access from Admins.');
                    }

                    $ua->setMyfootRole(null);
                    // also cannot do statements without access
                    $ua->setGetsStatements(0);
                }
            } else {
                throw new \Exception('You are not allowed to perform this action');
            }
            // only check and set password if it's passed in
            if ($checkPassword) {
                if ($pass == '') {
                    throw new \Exception('Please enter an initial password.');
                }

                if ($pass !== $passconfirm) {
                    throw new \Exception('Passwords do not match.');
                }

                \Genesis_Service_User::updatePassword($ua, $pass);
                $passwordWasSet = true;
            }

            // send notification email if requested
            if ($notify && $passwordWasSet) {
                if ($this->getLoggedUser()->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_GOD && $ua->getAccount()->getFirstAdmin()) {
                    $creator = $ua->getAccount()->getFirstAdmin();
                } else {
                    $creator = $this->getLoggedUser();
                }

                if ($ua->getEmail()) {
                    \Genesis_Service_UserAccess::sendNewUserEmail($ua, $creator, $pass);
                }
            }

            if ($ua->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_LIMITED && !$ua->getPassword() && !$passwordWasSet) {
                throw new \Exception('needpass.'); // this is interpreted on the front end to open the password dialog
            }

            \Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n");
        }

        return new Response('Success');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/getsstatements
     * http://localhost:9019/user/getsstatements
     */
    #[Route('/user/getsstatements', name: 'user_getsstatements', methods: ['POST'])]
    public function getsstatementsAction(Request $request): Response
    {
        $userId = $request->request->get('uid');
        $getsStatements = $request->request->get('getsStatements');

        try {
            $ua = \Genesis_Service_UserAccess::loadById($userId);

            // make sure if they're admins they cannot take this off
            if (!$getsStatements && $ua->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_ADMIN) {
                throw new \Exception('Admins must get statements.');
            }

            $ua->setGetsStatements($getsStatements);

            // must have access to myfoot for this
            if (!$ua->getMyfootRole()) {
                $ua->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_LIMITED);
            }

            \Genesis_Service_UserAccess::save($ua, $this->getLoggedUser());
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n");
        }

        return new Response('Success');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/updatefacs
     * http://localhost:9019/user/updatefacs
     */
    #[Route('/user/updatefacs', name: 'user_updatefacs', methods: ['POST'])]
    public function updatefacsAction(Request $request): Response
    {
        $user = $this->getloggedUser();
        $userId = $request->request->get('uid');
        $facIds = $request->request->get('facIds');
        $all = $this->getAllFacilityParam($facIds, $request);

        try {
            $ua = \Genesis_Service_UserAccess::loadById($userId);

            // remove any facility restrictions
            $facRestricts = \Genesis_Service_UserFacilityRestrictions::load(\Genesis_Db_Restriction::equal('userId', $userId));
            foreach ($facRestricts as $facRes) {
                \Genesis_Service_UserFacilityRestrictions::delete($facRes);
            }

            // get out of here if user is god
            if ($ua->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_GOD) {
                throw new \Exception('You cannot change facility access for god users.  They will be able to access all facilities.');
            }

            // get out of here if user is an admin
            if ($ua->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_ADMIN) {
                throw new \Exception('You cannot change facility access for admins.  They will be able to access all facilities.');
            }

            // if admin or god we would have already got outta here, so make sure they're full if they can access all or limited if not
            // but if no myfoot access previously or was geopage then keep it that way
            if ($ua->getMyfootRole()) {
                if ($all) {
                    $ua->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_FULL);
                } else {
                    $ua->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_LIMITED);
                }
            }

            if ($all) {
                $ua->setAllFacilities(1);
            } else {
                $ua->setAllFacilities(0);
            }

            // if there are facility id's then assign restrictions
            if ($facIds) {
                $facilityIds = explode(',', $facIds);

                // Validate ownership of facility IDs
                if (!$user->isMyFootGod()) {
                    // Fetch logged user's facilities
                    $allowedFacilitys = $user->getManagableFacilities()->toArray();
                    $allowedFacilityIds = array_map(function ($facility) {
                        return $facility->getId();
                    }, $allowedFacilitys);

                    foreach ($facilityIds as $facilityId) {
                        if (!in_array($facilityId, $allowedFacilityIds)) {
                            throw new \Exception("Unauthorized: Facility ID $facilityId does not belong to your account.");
                        }
                    }
                }

                // Add new restrictions for valid facilities
                foreach ($facilityIds as $facilityId) {
                    $facRes = new \Genesis_Entity_UserFacilityRestrictions();
                    $facRes->setFacilityId($facilityId);
                    $facRes->setUserId($userId);
                    \Genesis_Service_UserFacilityRestrictions::save($facRes);
                }
            }

            $ua = \Genesis_Service_UserAccess::save($ua, $user);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n");
        }

        return new Response('Success');
    }

    public function getAllFacilityParam($selectedFacilities, Request $request): bool
    {
        // if all facilities have been selected, return true
        if ($this->allFacilitiesSelected($selectedFacilities)) {
            return true;
        }

        return (bool) $request->request->get('all');
    }

    public function allFacilitiesSelected($selectedFacilities): bool
    {
        $user = $this->getLoggedUser();
        $account = $user->getAccount();
        $accountFacilities = \Genesis_Service_Facility::loadByAccountId(
            $account->getId(),
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('approved', 1),
                \Genesis_Db_Restriction::equal('published', 1)
            )
        )->toArray();

        $selectedFacilitiesArray = explode(',', $selectedFacilities);
        $accountFacilityIds = array_reduce($accountFacilities, function ($carry, \Genesis_Entity_Facility $facility) {
            $carry[] = $facility->getId();

            return $carry;
        }, []);

        $intersectedArray = array_intersect($accountFacilityIds, $selectedFacilitiesArray);

        return count($accountFacilityIds) == count($intersectedArray);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/getfacaccess
     * http://localhost:9019/user/getfacaccess
     */
    #[Route('/user/getfacaccess', name: 'user_getfacaccess', methods: ['POST'])]
    public function getfacaccessAction(Request $request): Response
    {
        $userId = $request->request->get('uid');

        try {
            $userAccess = \Genesis_Service_UserAccess::loadById($userId);

            if ($userAccess->canAccessAllFacilities()) {
                return new Response('All');
            } else {
                $ids = [];
                $facilities = $userAccess->getManagableFacilities()->toArray();
                foreach ($facilities as $fac) {
                    $ids[] = $fac->getId();
                }

                return new JsonResponse($ids);
            }
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n");
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/user/create
     * http://localhost:9019/user/create
     */
    #[Route('/user/create', name: 'user_create', methods: ['POST'])]
    public function createAction(Request $request): JsonResponse
    {
        $email = $request->request->get('email');
        $fname = $request->request->get('fname');
        $lname = $request->request->get('lname');
        $notify = $request->request->get('notify');
        $pass = $request->request->get('pass');
        $passconfirm = $request->request->get('passconfirm');
        $getsEmails = $request->request->get('getsEmails');
        $getsStatements = $request->request->get('getsStatements');
        $geopageOnly = $request->request->get('geopageOnly');
        $accessMyfoot = $request->request->get('accessMyfoot');
        $isAdmin = $request->request->get('isAdmin');
        $facilityIds = explode(',', $request->request->get('facilityIds', ''));
        $allFacs = $request->request->get('allFacs');

        try {
            if ($accessMyfoot) {
                if (!(strlen($pass) > 0)) {
                    throw new \Exception('Please enter an initial password.');
                }

                if ($pass != $passconfirm) {
                    throw new \Exception('Passwords do not match.');
                }
            }

            if (!(strlen($fname) > 0)) {
                throw new \Exception('Please enter a first name.');
            }

            if (!(strlen($lname) > 0)) {
                throw new \Exception('Please enter a last name.');
            }

            // validate email address
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('Please enter a valid email address.');
            }

            // check if this user already exists
            $user = \Genesis_Service_User::load(\Genesis_Db_Restriction::equal('email', $email))->uniqueResult();
            if (!$user) {
                $user = new \Genesis_Entity_User();
                $user->setEmail($email);
                $user->setFirstName($fname);
                $user->setLastName($lname);
                $user = \Genesis_Service_User::save($user);

                if ($accessMyfoot) {
                    \Genesis_Service_User::updatePassword($user, $pass);
                }
            }

            $newUserAccess = \Genesis_Service_UserAccess::loadById($user->getId());

            // create a new user if they're not yet in mysparefoot
            if (!$newUserAccess) {
                $newUserAccess = new \Genesis_Entity_UserAccess();
                $newUserAccess->setUserId($user->getId());
                $newUserAccess->setGetsStatements($getsStatements);
                $newUserAccess->setGetsEmails($getsEmails);
                $newUserAccess->setAccountId($this->getLoggedUser()->getAccountId());

                if ($isAdmin) {
                    $newUserAccess->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_ADMIN);
                    $newUserAccess->setAllFacilities(1);
                } elseif ($allFacs && !$geopageOnly) {
                    $newUserAccess->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_FULL);
                    $newUserAccess->setAllFacilities(1);
                } elseif ($accessMyfoot) {
                    $newUserAccess->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_LIMITED);
                }

                $newUserAccess = \Genesis_Service_UserAccess::save($newUserAccess, $this->getLoggedUser());

                // if this email is already a user but not an accout mgmt user then still update password and add them
                if ($accessMyfoot) {
                    \Genesis_Service_User::updatePassword($user, $passconfirm);
                }
            } else {
                if ($newUserAccess->getAccountId() == $this->getLoggedUser()->getAccountId()) {
                    throw new \Exception('User '.$email.' already exists as a user for your account.');
                } else {
                    throw new \Exception('User '.$email.' already has a MySpareFoot signin for another account.  <NAME_EMAIL> for help.');
                }
            }

            if ($request->request->get('facilityIds')) {
                if (!$this->getLoggedUser()->isMyFootGod()) {
                    $validFacilities = $this->getLoggedUser()->getManagableFacilities()->toArray();
                    foreach ($facilityIds as $facilityId) {
                        if (!in_array($facilityId, $validFacilities)) {
                            throw new \Exception("Unauthorized access: Facility ID {$facilityId} does not belong to your account.");
                        }
                    }
                }

                // no facility ids if allFacs=1
                foreach ($facilityIds as $facilityId) {
                    $facRes = new \Genesis_Entity_UserFacilityRestrictions();
                    $facRes->setFacilityId($facilityId);
                    $facRes->setUserId($user->getId());
                    \Genesis_Service_UserFacilityRestrictions::save($facRes);
                }
            }

            // send notification email if requested
            if ($notify) {
                if ($this->getLoggedUser()->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_GOD && $newUserAccess->getAccount()->getFirstAdmin()) {
                    $creator = $newUserAccess->getAccount()->getFirstAdmin();
                } else {
                    $creator = $this->getLoggedUser();
                }

                if ($email) {
                    \Genesis_Service_UserAccess::sendNewUserEmail($newUserAccess, $creator, $pass);
                }
            }

            $facNumAccess = 'All';
            if (!$newUserAccess->getMyfootRole()
                    || $newUserAccess->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_LIMITED) {
                $facNumAccess = count($newUserAccess->getManagableFacilities()->toArray());
            }

            $ret = [
                'email' => $email,
                'admin' => $isAdmin,
                'getsEmails' => $getsEmails,
                'getsStatements' => $getsStatements,
                'accessMyfoot' => $accessMyfoot,
                'numFacilities' => $facNumAccess,
                'facIds' => $facilityIds,
                'userId' => $user->getId(),
            ];

            return new JsonResponse($ret);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 400);
        }
    }
}
