<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PartnersController extends AbstractRestrictedController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/partners
     * http://localhost:9019/partners
     */
    #[Route('/partners', name: 'partners_index', methods: ['GET'])]
    public function indexAction(Request $request): Response
    {
        $this->view->scripts = ['partners/index'];

        return $this->render('partners/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    protected function getTab(): string
    {
        return ''; // No specific tab for partners page
    }
}
