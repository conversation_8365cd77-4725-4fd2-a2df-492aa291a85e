<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\Util;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class IndexController extends AbstractRestrictedController
{
    /**
     * Index action - redirects to appropriate landing page based on user permissions.
     *
     * Sample:
     * https://myfoot.sparefoot.com/
     * http://localhost:9019/
     */
    #[Route('/', name: 'index_index', methods: ['GET'])]
    public function indexAction(Request $request): Response
    {
        $landingPageUrl = Util::getMyFootLandingPage();

        return $this->redirect($landingPageUrl);
    }
}
