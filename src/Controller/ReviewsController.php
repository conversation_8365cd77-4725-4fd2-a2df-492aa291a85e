<?php

namespace Sparefoot\MyFootService\Controller;

use LightnCandy\LightnCandy;
use Sparefoot\EmailsServiceClient\Client\EmailsClient;
use Sparefoot\EmailsServiceClient\Util\Campaign;
use Sparefoot\EmailsServiceClient\Util\Email;
use Sparefoot\EmailsServiceClient\Util\Sender;
use Sparefoot\MyFootService\Models\BidIncreaseBannerValidation;
use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Service\Review;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReviewsController extends AbstractRestrictedController
{
    /**
     * Initialize common data for all review actions.
     */
    private function initializeReviewData(): array
    {
        $restriction = \Genesis_Db_Restriction::equal('published', 1);
        $restriction->setOrder(\Genesis_Db_Order::asc('title'));
        $facilities = $this->getLoggedUser()->getManagableFacilities($restriction);

        if (!count($facilities)) {
            // Note: In Symfony, we'd typically throw a redirect exception or return a redirect response
            // For now, we'll handle this in the calling method
            return ['facilities' => null, 'shouldRedirect' => true];
        }

        return [
            'facilities' => $facilities,
            'numManageableFacilities' => count($facilities->toArray()),
            'banner' => [
                'showNotificationBanner' => BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount()),
            ],
            'shouldRedirect' => false,
        ];
    }

    /**
     * Reviews index - Shows review management interface with facility filtering and search.
     *
     * GET /reviews - Shows all reviews
     * GET /reviews?fid=123 - Shows reviews for specific facility
     * GET /reviews?q=search - Search reviews
     * Sample:
     * https://myfoot.sparefoot.com/reviews
     * http://localhost:9019/reviews?fid=123&q=excellent
     */
    #[Route('/reviews', name: 'reviews_index', methods: ['GET'])]
    public function indexAction(Request $request): Response
    {
        $initData = $this->initializeReviewData();
        if ($initData['shouldRedirect']) {
            return $this->redirectToRoute('features_add_first');
        }

        $facilityId = $request->query->get('fid') ?: $this->getSession()->get('facilityId');
        $user = $this->getLoggedUser();

        // Check if the user has access to the facility
        if ($facilityId && !$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) {
            throw new \Exception('Unauthorized access to facility.', 403);
        }

        if (User::isFeatureActive(Features::REVIEWS_SINGLE_PAGE)) {
            // Single page application mode
            $facilityId = $this->getSession()->get('facilityId');
            $facilityEntity = \Genesis_Service_Facility::loadById($facilityId);
            $fields = ['title', 'company_code', 'active'];
            $facility = Facility::toArrayWithFields($facilityEntity, $fields);

            return $this->render('reviews/index_spa.html.twig', [
                'facility' => $facility,
                'facilities' => $initData['facilities'],
                'numManageableFacilities' => $initData['numManageableFacilities'],
                'banner' => $initData['banner'],
                'accountId' => $request->query->get('account_id'),
                'scripts' => [
                    '../dist/ember/features/assets/vendor',
                    '../dist/ember/features/assets/features',
                ],
                'layout' => 'layout-singlepage',
            ]);
        } else {
            // Traditional server-rendered mode
            $facility = null;
            if ($request->query->get('fid')) {
                $facility = \Genesis_Service_Facility::loadById($request->query->get('fid'));
                $restriction = \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::equal('listingAvailId', $facility->getId()),
                    \Genesis_Db_Restriction::isNull('parentId')
                );
            } else {
                $restriction = \Genesis_Db_Restriction::isNull('parentId');
            }

            $searchQuery = $request->query->get('q', '');
            if ($searchQuery) {
                $reviews = \Genesis_Service_Review::loadBySearch($this->getLoggedUser(), $searchQuery, $restriction);
            } else {
                $reviews = \Genesis_Service_Review::loadReviewsByRestrictedUserId($this->getLoggedUser(), $restriction);
            }

            return $this->render('reviews/index.html.twig', [
                'title' => 'Reviews',
                'facility' => $facility,
                'reviews' => $reviews,
                'q' => $searchQuery,
                'facilities' => $initData['facilities'],
                'numManageableFacilities' => $initData['numManageableFacilities'],
                'banner' => $initData['banner'],
                'accountId' => $request->query->get('account_id'),
                'scripts' => ['reviews/index'],
            ]);
        }
    }

    /**
     * Review detail - Shows individual review details and responses.
     *
     * GET /reviews/detail/{rid} - Shows review detail
     * Sample:
     * https://myfoot.sparefoot.com/reviews/detail/12345
     * http://localhost:9019/reviews/detail/12345
     */
    #[Route('/reviews/detail/{rid}', name: 'reviews_detail', methods: ['GET'], requirements: ['rid' => '\d+'])]
    public function detailAction(Request $request, int $rid): Response
    {
        $review = \Genesis_Service_Review::loadById($rid);

        return $this->render('reviews/detail.html.twig', [
            'review' => $review,
            'scripts' => ['reviews/detail'],
        ]);
    }

    /**
     * Submit review response - Manager responds to customer review.
     *
     * POST /reviews/response - Submit manager response
     * Body: {response: "text", parent_id: 123}
     * Sample:
     * https://myfoot.sparefoot.com/reviews/response
     * http://localhost:9019/reviews/response
     */
    #[Route('/reviews/response', name: 'reviews_response', methods: ['POST'])]
    public function responseAction(Request $request): JsonResponse
    {
        $output = ['success' => 1];

        try {
            $response = $request->request->get('response');
            $parentId = $request->request->get('parent_id');

            if (!$response || !$parentId) {
                throw new \Exception('Response and parent_id are required.');
            }

            $parentReview = \Genesis_Service_Review::loadById($parentId);
            if (empty($parentReview)) {
                \Genesis_Util_ErrorLogger::errorHandler(E_ERROR, 'Cannot load parent review with ID: '.$parentId, __FILE__, __LINE__);
                throw new \Exception('Cannot load parent review with ID: '.$parentId);
            }

            $user = $this->getLoggedUser();

            // Check if the logged-in user has access to the facility
            if (!$user->isMyFootGod() && !in_array($parentReview->getFacilityId(), $user->getManageableFacilityIds())) {
                throw new \Exception('Unauthorized access to facility.', 403);
            }

            $review = new \Genesis_Entity_Review();
            $review->setListingAvailId($parentReview->getFacilityId());
            $review->setTitle("Manager's Response");
            $review->setMessage($response);
            $review->setNickname('Manager Response');
            $review->setRating(0);
            $review->setIpAddress(ip2long($request->getClientIp()));
            $review->setSource('MyFootResponse');
            $review->setParentId($parentReview->getId());
            $review->setUserId($user->getId());
            $review->setStatus(\Genesis_Entity_Review::STATUS_PENDING);
            $review->setSiteId(0);

            if ($request->cookies->has('visit')) {
                $review->setVisitId($request->cookies->get('visit'));
            }

            $updatedReview = \Genesis_Service_Review::save($review, false, $user);
            $output['review'] = Review::toArray($updatedReview);
        } catch (\Exception $e) {
            $output['success'] = 0;
            $output['msg'] = $e->getMessage();
        }

        return new JsonResponse($output);
    }

    /**
     * Request reviews from customers - Send review request emails to tenant list.
     *
     * GET /reviews/request/{fid} - Show request form
     * POST /reviews/request/{fid} - Send review requests
     * Body: {emails: "email1,email2", facility_id: 123}
     * Sample:
     * https://myfoot.sparefoot.com/reviews/request/123
     * http://localhost:9019/reviews/request/123
     */
    #[Route('/reviews/request/{fid}', name: 'reviews_request', methods: ['GET', 'POST'], requirements: ['fid' => '\d+'])]
    public function requestAction(Request $request, ?int $fid = null): Response
    {
        $facilityId = $fid ?: $this->getSession()->get('facilityId');
        $facility = \Genesis_Service_Facility::loadById($facilityId);

        $esClient = new EmailsClient();
        $emailTemplate = $this->getEmailPreview($esClient, $facility);

        $templateData = [
            'facility' => $facility,
            'emailTemplate' => $emailTemplate,
            'scripts' => ['reviews/request'],
        ];

        if ($request->isMethod('POST')) {
            try {
                $emails = $request->request->get('emails');
                $facId = $request->request->get('facility_id');

                $facility = \Genesis_Service_Facility::loadById($facId);

                if (!$facility) {
                    throw new \Exception('We could not find your facility in our system.');
                }

                if (!$emails) {
                    throw new \Exception('Please enter email addresses separated by commas.');
                }

                if (strlen($emails) > 2500) {
                    throw new \Exception('Maximum length of 2500 was exceeded; please enter fewer emails at a time.');
                }

                // Clean and parse email addresses
                $emails = str_replace([';', "\n"], ',', $emails);
                $emails = preg_replace('/\s+/', ',', $emails);
                $emails = array_unique(explode(',', $emails));

                $invalidEmails = [];
                foreach ($emails as $email) {
                    $email = trim($email);
                    if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $invalidEmails[] = $email;
                    }
                }

                if ($invalidEmails) {
                    $exceptionText = 'Invalid email address(es) detected: '.implode(', ', $invalidEmails);
                    throw new \Exception($exceptionText);
                }

                $emailFailures = [];

                // Send review request email to each address
                foreach ($emails as $email) {
                    $email = trim($email);
                    if (!$email) {
                        continue;
                    }

                    // Create a user for anyone not in the system
                    $user = \Genesis_Service_User::loadByEmail($email);
                    if (!$user) {
                        $newUser = new \Genesis_Entity_User();
                        $newUser->setEmail(trim(strtolower($email)));
                        $user = \Genesis_Service_User::save($newUser);
                    }

                    // Validation checks
                    if (!$this->validateEmailForReviewRequest($email, $facility, $user, $emailFailures)) {
                        continue;
                    }

                    try {
                        // Send email via EmailsService
                        $emailMessage = new Email($email, Sender::SPAREFOOT, Campaign::CONSUMER_REVIEWREQUEST);
                        $emailMessage->setMergeVars([
                            ['name' => 'facility', 'content' => \Genesis_Util_EmailMapper::facilityToArray($facility)],
                            ['name' => 'admin', 'content' => \Genesis_Util_EmailMapper::userToArray($this->getLoggedUser())],
                        ]);

                        $emailMessage->setParams([
                            'admin_id' => $this->getLoggedUser()->getUserId(),
                            'facility_id' => $facility->getId(),
                        ]);

                        $esClient->sendEmailSqs($emailMessage);
                    } catch (\Exception $e) {
                        $emailFailures[] = [
                            'email' => $email,
                            'error' => 'they\'ve requested not to receive emails',
                        ];
                    }
                }

                // Log the action
                $logger = new \Genesis_Util_ActionLogger();
                $logger->logAction('myfoot_reviews_send_request', null, null, $this->getLoggedUser()->getId(), $facId, null);

                $templateData['emails'] = $request->request->get('emails');
                $templateData['emailFailures'] = $emailFailures;
                $templateData['alertType'] = 'success';
                $templateData['alertMessage'] = '<strong>Sent!</strong> Keep those good reviews flowing in.';
            } catch (\Exception $e) {
                $templateData['alertType'] = 'danger';
                $templateData['alertMessage'] = '<strong>Oops!</strong> '.$e->getMessage();
            }
        }

        return $this->render('reviews/request.html.twig', $templateData);
    }

    /**
     * Validate email for review request eligibility.
     */
    private function validateEmailForReviewRequest(string $email, $facility, $user, array &$emailFailures): bool
    {
        // Check if user has a confirmed booking
        $transaction = \Genesis_Service_Transaction::loadByEmail($email,
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                \Genesis_Db_Restriction::equal('bookingState', \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
            )
        )->uniqueResult();

        if (!$transaction) {
            $emailFailures[] = [
                'email' => $email,
                'error' => 'you can only request reviews from tenants who have successfully moved in.',
            ];

            return false;
        }

        // Check if move-in was more than 30 days ago
        $recentTransaction = \Genesis_Service_Transaction::loadByEmail($email,
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                \Genesis_Db_Restriction::greaterThan('moveIn', date('Y-m-d', strtotime('-30 days')))
            )
        )->uniqueResult();

        if ($recentTransaction) {
            $emailFailures[] = [
                'email' => $email,
                'error' => 'they haven\'t been in the facility for over 30 days',
            ];

            return false;
        }

        // Check if user already left a review
        $existingReview = \Genesis_Service_Review::loadByFacilityId($facility->getId(),
            \Genesis_Db_Restriction::equal('userId', $user->getId()))->uniqueResult();

        if ($existingReview) {
            $emailFailures[] = [
                'email' => $email,
                'error' => 'they\'ve already left a review',
            ];

            return false;
        }

        // Check if user is affiliated with an account (facility client)
        $ua = \Genesis_Service_UserAccess::loadById($user->getId());
        if ($ua && $ua->getAccountId()) {
            $emailFailures[] = [
                'email' => $email,
                'error' => 'they\'re a SpareFoot facility client like yourself',
            ];

            return false;
        }

        return true;
    }

    /**
     * Generate email template preview for review request.
     */
    private function getEmailPreview(EmailsClient $client, $facility): string
    {
        try {
            $template = $client->getTemplate(Sender::SPAREFOOT, Campaign::CONSUMER_REVIEWREQUEST);

            // Parse the innerHTML of the body tag
            $startBodyTag = strpos($template['markup'], '<body');
            $endBodyTag = strpos($template['markup'], '>', $startBodyTag) + 1;
            $end = strpos($template['markup'], '</body>');
            $template = substr($template['markup'], $endBodyTag, $end - $endBodyTag);

            // Prepare template
            $tmpl = LightnCandy::compile($template);
            $renderer = LightnCandy::prepare($tmpl);

            return $renderer([
                'facility' => \Genesis_Util_EmailMapper::facilityToArray($facility),
                'admin' => \Genesis_Util_EmailMapper::userToArray($this->getLoggedUser()),
            ]);
        } catch (\Exception $e) {
            return 'Failed to retrieve email template';
        }
    }
}
