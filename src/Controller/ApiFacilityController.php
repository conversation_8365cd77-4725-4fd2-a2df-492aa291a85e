<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Account;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Service\Inquiry;
use Sparefoot\MyFootService\Service\Review;
use Sparefoot\MyFootService\Service\Unit;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiFacilityController extends ApiBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}
     * http://localhost:9019/api/facilities/{FACILITY_ID}
     */
    #[Route('/api/facilities/{facility_id}{slash}', name: 'api_facilities_show', requirements: ['slash' => '/?', 'facility_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET', 'POST', 'PUT'])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $facilityId = $request->get('facility_id');
        $user = $this->getLoggedUser();
        if ($user && !$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) {
            throw new ApiException(ApiException::UNAUTHORIZED, 'Unauthorized access to facility.');
        }

        if ($request->isMethod('GET') || $request->isMethod('PUT')) {
            $checkWriteAccess = $request->isMethod('GET') ? false : true;
            $facility = $this->validateAndGetFacility($facilityId, $checkWriteAccess);
        }

        $json_body = $request->getContent();

        // GET - handle get requests
        if ($request->isMethod('GET')) {
            return $this->json(['data' => Facility::toArray($facility)]);
        }
        if ($request->isMethod('PUT')) {
            $activePreValue = $facility->getActive();

            $user = $this->getLoggedUser();
            /* @var \Genesis_Entity_UserAccess $user */
            Facility::updateFromJson($facility, $json_body, $user);

            // Reload facility now that it has been updated in db and log this activity if facility active has changed
            $facility = $this->validateAndGetFacility($facilityId);
            $activePostValue = $facility->getActive();
            if ($activePreValue != $activePostValue) {
                $context['account_id'] = $facility->getAccountId();
                $context['hiding'] = $activePostValue;
                // TODO: Log facility toggle activity when stats service is available
            }

            return $this->json(['data' => Facility::toArray($facility)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/account_id/{ACCOUNT_ID}
     * http://localhost:9019/api/facilities/account_id/{ACCOUNT_ID}
     */
    #[Route('/api/facilities/account_id/{account_id}{slash}', name: 'api_facilities_by_account', requirements: ['slash' => '/?', 'account_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function getallfacilitiesforaccountAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $accountId = $request->get('account_id');
        if (!$accountId) {
            throw new ApiException(ApiException::BAD_REQUEST, 'account_id is required');
        }
        $account = \Genesis_Service_Account::loadById($accountId);
        if (!$account) {
            throw new ApiException(ApiException::BAD_REQUEST, 'no such account');
        }
        try {
            User::validateAccountAccess($account);
        } catch (\Exception $e) {
            // throw new ApiException(ApiException::FORBIDDEN, 'not authorized to load facilities from account ' . $accountId);
        }

        $limitedFields = $request->get('fields');
        if ($limitedFields) {
            $whiteList = [
                'title',
                'company_code',
                'active',
            ];

            // Only accept alpha numeric, _, and comma.
            $limitedFields = preg_replace('/[^\w,]/', '', $limitedFields);
            $unfilteredFieldList = explode(',', $limitedFields);
            $limitedFieldsList = array_values(array_intersect($unfilteredFieldList, $whiteList));
        }

        $facilities = Account::getAllFacilities($accountId);
        $facilitiesArray = [];
        foreach ($facilities as $facility) {
            if (User::canAccessFacility($facility)) {
                if ($limitedFieldsList) {
                    $facilitiesArray[] = Facility::toArrayWithFields($facility, $limitedFieldsList);
                } else {
                    $facilitiesArray[] = Facility::toArray($facility);
                }
            }
        }
        $response = [
            'meta' => [
                'count' => count($facilitiesArray),
            ],
            'data' => $facilitiesArray,
            'account_id' => $accountId,
        ];

        return $this->json($response);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}/units
     * http://localhost:9019/api/facilities/{FACILITY_ID}/units
     */
    #[Route('/api/facilities/{facility_id}/units{slash}', name: 'api_facilities_units', requirements: ['slash' => '/?', 'facility_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET', 'POST'])]
    public function unitsAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $json_body = $request->getContent();
        $facilityId = $request->get('facility_id');
        $checkWriteAccess = $request->isMethod('GET') ? false : true;
        $facility = $this->validateAndGetFacility($facilityId, $checkWriteAccess);

        // GET - handle get requests
        if ($request->isMethod('GET')) {
            return $this->json(['data' => Facility::getAllUnitsArray($facility)]);
        }

        if ($request->isMethod('POST')) {
            $decoded_json = json_decode($json_body);
            if (!$decoded_json) {
                throw new ApiException(ApiException::BAD_REQUEST, 'invalid json body');
            }
            // we always set this from the url at this route
            $decoded_json->facility_id = $facility->getId();

            try {
                Unit::validateInsert($decoded_json);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            $unit = Unit::getNewUnitFromType($decoded_json->unit_type);
            $unit->setFacilityId($facility->getId());
            $unitArray = Unit::upsertFromJson($unit, json_encode($decoded_json));

            return $this->json(['data' => $unitArray]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}/photos/{PHOTO_ID}
     * http://localhost:9019/api/facilities/{FACILITY_ID}/photos/{PHOTO_ID}
     */
    #[Route('/api/facilities/{facility_id}/photos/{photo_id}{slash}', name: 'api_facilities_photos', requirements: ['slash' => '/?', 'facility_id' => '\d+', 'photo_id' => '\d+'], defaults: ['slash' => '', 'photo_id' => null], methods: ['GET', 'POST', 'PUT', 'DELETE'])]
    public function photosAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $facilityId = $request->get('facility_id'); // must be set
        $photoId = $request->get('photo_id'); // maybe set
        $json_body = $request->getContent(); // might be set

        $checkWriteAccess = $request->isMethod('GET') ? false : true;
        $facility = $this->validateAndGetFacility($facilityId, $checkWriteAccess);

        $user = $this->getLoggedUser();
        if ($user && !$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) {
            throw new ApiException(ApiException::UNAUTHORIZED, 'Unauthorized access to facility.');
        }

        if ($request->isMethod('GET')) {
            if ($photoId) {
                try { // valid photo id is required
                    $photo = Facility::validatePhotoId($photoId, $facility);
                } catch (\Exception $e) {
                    throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
                }

                return $this->json(['data' => Facility::serializeFacilityPhoto($photo)]);
            }

            return $this->json(['data' => Facility::serializeFacilityPhotos($facility)]);
        }

        if ($request->isMethod('POST')) {
            $savedPhotos = Facility::savePhotosFromUpload($facility);
            $return = [];

            foreach ($savedPhotos as $photo) {
                $return[] = Facility::serializeFacilityPhoto($photo);
            }

            return $this->json(['data' => $return]);
        }

        if ($request->isMethod('PUT')) {
            try { // valid photo id is required
                $photo = Facility::validatePhotoId($photoId, $facility);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            // This route only handles default:true
            $decoded_json = json_decode($json_body);
            if ($decoded_json->default) {
                try {
                    \Genesis_Service_FacilityImage::setDefault($photo->getFacilityId(), $photo->getPictureNum());
                    $photo = \Genesis_Service_FacilityImage::loadById($photoId);
                } catch (\Exception $e) {
                    throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
                }
            }

            return $this->json(['data' => Facility::serializeFacilityPhoto($photo)]);
        }

        if ($request->isMethod('DELETE')) {
            try { // valid photo id is required
                $photo = Facility::validatePhotoId($photoId, $facility);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            try {
                \Genesis_Service_FacilityImage::delete($facilityId, $photo->getPictureNum());
            } catch (\Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }

            return $this->json(['data' => Facility::serializeFacilityPhotos($facility)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}/bookings
     * http://localhost:9019/api/facilities/{FACILITY_ID}/bookings
     */
    #[Route('/api/facilities/{facility_id}/bookings{slash}', name: 'api_facilities_bookings', requirements: ['slash' => '/?', 'facility_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function bookingsAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $facilityId = $request->get('facility_id');
        $facility = $this->validateAndGetFacility($facilityId);

        if ($request->isMethod('GET')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            self::validateStartEndDate($startDate, $endDate);

            $bookings = Facility::serializeFacilityBookings($facility, $startDate, $endDate, $request->query->all());
            $meta = [
                'count' => sizeof($bookings),
            ];

            return $this->json(['data' => $bookings, 'meta' => $meta]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}/inquiries
     * http://localhost:9019/api/facilities/{FACILITY_ID}/inquiries
     */
    #[Route('/api/facilities/{facility_id}/inquiries{slash}', name: 'api_facilities_inquiries', requirements: ['slash' => '/?', 'facility_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function inquiriesAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $facilityId = $request->get('facility_id');
        $facility = $this->validateAndGetFacility($facilityId);

        if ($request->isMethod('GET')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            self::validateStartEndDate($startDate, $endDate);

            $inquiries = Facility::serializeFacilityInquiries($facility, $startDate, $endDate);
            $meta = [
                'count' => sizeof($inquiries),
            ];

            return $this->json(['data' => $inquiries, 'meta' => $meta]);
        }
        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}/inquiries/{INQUIRY_ID}
     * http://localhost:9019/api/facilities/{FACILITY_ID}/inquiries/{INQUIRY_ID}
     */
    #[Route('/api/facilities/{facility_id}/inquiries/{inquiry_id}{slash}', name: 'api_facilities_inquiry', requirements: ['slash' => '/?', 'facility_id' => '\d+', 'inquiry_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function inquiryAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $facilityId = $request->get('facility_id');
        $inquiryId = $request->get('inquiry_id');
        $facility = $this->validateAndGetFacility($facilityId);
        $inquiry = Inquiry::validateInquiryId($inquiryId);

        if ($inquiry->getListingAvailId() != $facilityId) {
            // throw new ApiException(ApiException::FORBIDDEN, 'Inquiry does not belong to facility');
        }

        if ($request->isMethod('GET')) {
            return $this->json(['data' => Inquiry::toArray($inquiry)]);
        }
        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}/reviews
     * http://localhost:9019/api/facilities/{FACILITY_ID}/reviews
     */
    #[Route('/api/facilities/{facility_id}/reviews{slash}', name: 'api_facilities_reviews', requirements: ['slash' => '/?', 'facility_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function reviewsAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $facility_id = $request->get('facility_id');
        $user = $this->getLoggedUser();

        // Check if the user has access to the facility
        if ($user && !$user->isMyFootGod() && !in_array($facility_id, $user->getManageableFacilityIds())) {
            throw new ApiException(ApiException::FORBIDDEN, 'Unauthorised access to reviews');
        }
        $facility = $this->validateAndGetFacility($facility_id);
        $reviews = $facility->getFacilityReviews();

        if ($request->isMethod('GET')) {
            return $this->json(['data' => Facility::serializeFacilityReviews($facility)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/facilities/{FACILITY_ID}/reviews/{REVIEW_ID}
     * http://localhost:9019/api/facilities/{FACILITY_ID}/reviews/{REVIEW_ID}
     */
    #[Route('/api/facilities/{facility_id}/reviews/{review_id}{slash}', name: 'api_facilities_review', requirements: ['slash' => '/?', 'facility_id' => '\d+', 'review_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function singleReviewAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $facility_id = $request->get('facility_id');
        $review_id = $request->get('review_id');
        $facility = $this->validateAndGetFacility($facility_id);

        $user = $this->getLoggedUser();

        // Check if the user has access to the facility
        if ($user && !$user->isMyFootGod() && !in_array($facility_id, $user->getManageableFacilityIds())) {
            throw new ApiException(ApiException::FORBIDDEN, 'Unauthorised access to reviews');
        }
        $review = Review::validateAndGetReview($review_id, $facility_id);
        if ($request->isMethod('GET')) {
            return $this->json(['data' => Review::toArray($review)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }
}
