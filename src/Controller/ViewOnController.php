<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Facility;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * View On Controller.
 *
 * Serves as a smart redirect for a facility to a site
 * TODO: Support other sites besides SpareFoot.com
 */
class ViewOnController extends AbstractPublicController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/view-on/facility/{facility_id}?site_id={site_id}
     * http://localhost:9019/view-on/facility/{facility_id}?site_id={site_id}
     */
    #[Route('/view-on/facility/{facility_id}', name: 'view_on_facility', requirements: ['facility_id' => '\d+'], methods: ['GET'])]
    public function facilityAction(Request $request): Response
    {
        $facilityId = $request->get('facility_id');

        try {
            $facility = Facility::validateFacilityId($facilityId);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }

        $siteId = $request->query->get('site_id');
        // TODO: Add support in the future for more sites besides SFDC

        // SpareFoot.com URL
        $url = \Genesis_Util_Url::facilityUrl($facility);

        // Redirect
        return $this->redirect($url);
    }
}
