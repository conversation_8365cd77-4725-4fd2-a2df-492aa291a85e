<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class MoveInController extends AbstractPublicController
{
    protected function _init(): void
    {
        // Layout handling moved to template level
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/move-in/yes/email/<EMAIL>/confirmation/FM7ENTW8
     * http://localhost:9019/move-in/yes/email/<EMAIL>/confirmation/FM7ENTW8
     */
    #[Route('/move-in/yes/email/{email}/confirmation/{confirmation}', name: 'move_in_yes', methods: ['GET'])]
    public function yesAction(Request $request): Response
    {
        $confCode = $request->get('confirmation');
        $confirmingUserEmail = urldecode($request->get('email'));

        $user = \Genesis_Service_User::loadByEmail($confirmingUserEmail);
        $transaction = \Genesis_Service_Transaction::loadById($confCode);

        if (empty($transaction) || empty($user)) {
            return $this->redirectToRoute('login');
        }

        $transaction->facilityConfirmMovein($user);

        return $this->render('move-in/index.html.twig');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/move-in/no/email/<EMAIL>/confirmation/FM7ENTW8
     * http://localhost:9019/move-in/no/email/<EMAIL>/confirmation/FM7ENTW8
     */
    #[Route('/move-in/no/email/{email}/confirmation/{confirmation}', name: 'move_in_no', methods: ['GET'])]
    public function noAction(Request $request): Response
    {
        $confCode = $request->get('confirmation');
        $confirmingUserEmail = urldecode($request->get('email'));

        $user = \Genesis_Service_User::loadByEmail($confirmingUserEmail);
        $transaction = \Genesis_Service_Transaction::loadById($confCode);

        if (empty($transaction) || empty($user)) {
            return $this->redirectToRoute('login');
        }

        $transaction->facilityConfirmNoMovein($user);

        return $this->render('move-in/no.html.twig', [
            'noMovein' => true,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/move-in/maybe/email/<EMAIL>/confirmation/FM7ENTW8
     * http://localhost:9019/move-in/maybe/email/<EMAIL>/confirmation/FM7ENTW8
     */
    #[Route('/move-in/maybe/email/{email}/confirmation/{confirmation}', name: 'move_in_maybe', methods: ['GET'])]
    public function maybeAction(Request $request): Response
    {
        // Don't save another action log if this is a result of the GIZMO crap...
        if (!$request->get('sg_sessionid')) {
            $confCode = $request->get('confirmation');
            $confirmingUserEmail = urldecode($request->get('email'));

            $user = \Genesis_Service_User::loadByEmail($confirmingUserEmail);
            $transaction = \Genesis_Service_Transaction::loadById($confCode);

            if (empty($transaction) || empty($user)) {
                return $this->redirectToRoute('login');
            }

            $transaction->facilityConfirmFutureMovein($user);

            return $this->render('move-in/maybe.html.twig');
        }

        return new Response();
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/move-in/opt-out?t=reconciliation-warning&e=<EMAIL>&sb=123
     * http://localhost:9019/move-in/opt-out?t=reconciliation-warning&e=<EMAIL>&sb=123
     */
    #[Route('/move-in/opt-out', name: 'move_in_opt_out', methods: ['GET'])]
    public function optOutAction(Request $request): Response
    {
        $templateLabel = $request->get('t');
        $emailAddress = $request->get('e');
        $statementBatchId = $request->get('sb');

        try {
            if (!$statementBatchId) {
                $openStatementBatch = \Genesis_Service_StatementBatch::loadByStatus(\Genesis_Entity_StatementBatch::STATUS_OPEN);
                if ($openStatementBatch) {
                    $statementBatchId = $openStatementBatch->getId();
                } else {
                    // This is in case this is hit outside of an open statement period, it should add the opt-out, but be ignored
                    $statementBatchId = '00';
                }
            }

            $optOutableTemplates = [
                'reconciliation-warning' => 'facility/reconciliation-warning.'.$statementBatchId,
            ];

            if (isset($optOutableTemplates[$templateLabel])) {
                $messageName = $optOutableTemplates[$templateLabel];
            } else {
                $messageName = $optOutableTemplates['reconciliation-warning'];
            }

            $optout = \Genesis_Service_EmailOptOut::add($emailAddress, \Genesis_Service_EmailOptOut::buildFromTemplatePath($messageName));
            if (!$optout) {
                throw new \Exception('Could not opt out');
            }

            return $this->render('move-in/opt-out.html.twig', [
                'email' => $emailAddress,
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
