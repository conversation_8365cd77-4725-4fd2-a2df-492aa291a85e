<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\Booking;
use Sparefoot\MyFootService\Service\UserAuthByBooking;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class BookingController extends AbstractRestrictedController
{
    private $booking;
    private $user;

    /**
     * Sample:
     * https://myfoot.sparefoot.com/booking/move-in?s=SECRET&confirmation_code=CODE&email=EMAIL
     * http://localhost:9019/booking/move-in?s=SECRET&confirmation_code=CODE&email=EMAIL
     */
    #[Route('/booking/move-in', name: 'booking_move_in', methods: ['GET'])]
    public function moveInAction(Request $request): Response
    {
        $this->initializeBooking($request);

        return $this->render('booking/move-in.html.twig', [
            'scripts' => [
                '../dist/ember/features/assets/vendor',
                '../dist/ember/features/assets/features',
            ],
            'authBookingToken' => $this->view->authBookingToken,
            'facility' => $this->view->facility,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/booking/remind-me-later?s=SECRET&confirmation_code=CODE&email=EMAIL
     * http://localhost:9019/booking/remind-me-later?s=SECRET&confirmation_code=CODE&email=EMAIL
     */
    #[Route('/booking/remind-me-later', name: 'booking_remind_me_later', methods: ['POST'])]
    public function remindMeLaterAction(Request $request): Response
    {
        $this->initializeBooking($request);

        $this->booking->setBookingDataAttr(\Genesis_Entity_BookingData::POST_MOVE_IN_EMAIL_REMINDER_DATE, date('Y-m-d', strtotime('+10 day')));
        \Genesis_Service_Transaction::updateBookingData($this->booking);

        // Action Log
        $this->booking->facilityConfirmFutureMovein($this->user);

        return $this->render('booking/remind-me-later.html.twig', [
            'facility' => $this->view->facility,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/booking/deny?s=SECRET&confirmation_code=CODE&email=EMAIL
     * http://localhost:9019/booking/deny?s=SECRET&confirmation_code=CODE&email=EMAIL
     */
    #[Route('/booking/deny', name: 'booking_deny', methods: ['GET'])]
    public function denyAction(Request $request): Response
    {
        $this->initializeBooking($request);

        return $this->render('booking/deny.html.twig', [
            'facility' => $this->view->facility,
        ]);
    }

    private function initializeBooking(Request $request): void
    {
        // Inject the Auth Booking Token into the Page
        $secret = $request->query->get('s');
        $bookingConfirmationCode = $request->query->get('confirmation_code');
        $email = $request->query->get('email');
        $this->view->authBookingToken = UserAuthByBooking::serializeToken($secret, $bookingConfirmationCode, $email);

        $this->user = \Genesis_Service_User::loadByEmail($email);

        $confirmation_code = $request->query->get('confirmation_code');
        $this->booking = $booking = Booking::validateAndGetBooking($confirmation_code);
        $this->view->facility = $booking->getFacility();
    }
}
