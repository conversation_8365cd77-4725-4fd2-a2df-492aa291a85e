<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\Util;
use Sparefoot\MyFootService\Utils\CsrfUtil;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SignupEndController extends AbstractRestrictedController
{
    public const SIGNUP_END_CSRF_TOKEN = 'signup_end_csrf_token';

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/terms
     * http://localhost:9019/signup-end/terms
     */
    #[Route('/signup-end/terms', name: 'signup_end_terms', methods: ['GET', 'POST'])]
    public function termsAction(Request $request): Response
    {
        $user = $this->getLoggedUser();
        $account = $user->getAccount();
        $termsVersion = \Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION;

        $templateData = [
            'action' => 'terms',
            'loggedUser' => $user,
            'csrf_token' => CsrfUtil::getToken(self::SIGNUP_END_CSRF_TOKEN),
            'scripts' => ['signup-end/terms'],
            'backlink' => '/signup-start/company/',
            'termsVersion' => $termsVersion,
            'account' => $account,
            'bidType' => $account->getBidType(),
            'agree1' => $account->getTermsVersion() ? 1 : $request->request->get('agree1', false),
            'agree2' => $account->getTermsVersion() ? 1 : $request->request->get('agree2', false),
            'agree3' => $account->getTermsVersion() ? 1 : $request->request->get('agree3', false),
            'agree4' => $account->getTermsVersion() ? 1 : $request->request->get('agree4', false),
        ];

        if ($request->isMethod('POST')) {
            $agree1 = $request->request->get('agree1', false);
            $agree2 = $request->request->get('agree2', false);
            $agree3 = $request->request->get('agree3', false);
            $agree4 = $request->request->get('agree4', false);

            if (
                ($account->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT || $agree1 === 'on')
                && $agree2 === 'on'
                && $agree3 === 'on'
                && $agree4 === 'on'
            ) {
                if (!CsrfUtil::validateToken(self::SIGNUP_END_CSRF_TOKEN, $request->request->get('csrf_token'))) {
                    $templateData['error'] = 'There was an error during the submission. Please refresh and try again.';
                } else {
                    // Save terms info
                    $account->setTermsVersion($termsVersion);
                    $account->setTermsAgreedByUserId($user->getId());
                    $account->setTermsAgreedDate(date('Y-m-d H:i:s'));

                    \Genesis_Service_Account::save($account);

                    return $this->redirectToRoute('signup_end_billing');
                }
            }
        }

        // Make sure the user can go back to the other controller
        $this->getSession()->set('userId', $user->getId());

        return $this->render('signup-end/terms.html.twig', $templateData, new Response('', 200, [
            'X-Layout' => 'signup-layout',
        ]));
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/billing
     * http://localhost:9019/signup-end/billing
     */
    #[Route('/signup-end/billing', name: 'signup_end_billing', methods: ['GET'])]
    public function billingAction(Request $request): Response
    {
        $user = $this->getLoggedUser();
        $account = \Genesis_Service_Account::loadById($user->getAccountId());

        // Check if billing is already set up
        $be = \Genesis_Service_BillableEntity::loadByAccount($account)->uniqueResult();
        $completed = $be ? 'true' : 'false';

        $templateData = [
            'action' => 'billing',
            'loggedUser' => $user,
            'backlink' => '/signup-end/terms/',
            'csrf_token' => CsrfUtil::getToken(self::SIGNUP_END_CSRF_TOKEN),
            'user' => $user,
            'accountId' => $account->getAccountId(),
            'completed' => $completed,
            'beId' => 0,
            'emails' => $user->getEmail(),
            'paymentTypeNickname' => '',
            'address' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
            'creditCardNumber' => '',
            'ccType' => '',
            'ccNsId' => '',
            'ccName' => '',
            'ccExpM' => '',
            'ccExpY' => '',
            'scripts' => ['signup-end/billing'],
        ];

        return $this->render('signup-end/billing.html.twig', $templateData, new Response('', 200, [
            'X-Layout' => 'signup-layout',
        ]));
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/software
     * http://localhost:9019/signup-end/software
     */
    #[Route('/signup-end/software', name: 'signup_end_software', methods: ['GET'])]
    public function softwareAction(Request $request): Response
    {
        $user = $this->getLoggedUser();
        $account = \Genesis_Service_Account::loadById($user->getAccountId());

        $templateData = [
            'action' => 'software',
            'loggedUser' => $user,
            'myFootLink' => Util::getMyFootLandingPage(),
            'scripts' => ['signup-end/software'],
            'backlink' => '/signup-end/billing',
        ];

        return $this->render('signup-end/software.html.twig', $templateData, new Response('', 200, [
            'X-Layout' => 'signup-layout',
        ]));
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/record-software
     * http://localhost:9019/signup-end/record-software
     */
    #[Route('/signup-end/record-software', name: 'signup_end_record_software', methods: ['POST'])]
    public function recordSoftwareAction(Request $request): JsonResponse
    {
        try {
            $user = $this->getLoggedUser();
            $account = \Genesis_Service_Account::loadById($user->getAccountId());

            // Record their software types
            $integrationType = $request->request->get('integration_type');
            if (is_array($integrationType)) {
                foreach ($integrationType as $key => $val) {
                    $software = new \Genesis_Entity_AccountSoftware();
                    $software->setAccountId($account->getId());
                    $software->setSourceId($val);
                    $software->setUserId($user->getId());
                    \Genesis_Service_AccountSoftware::save($software);
                }
            }

            $facilityName = $request->request->get('facility_name');
            if ($facilityName) {
                // Make the first facility
                $corp = \Genesis_Service_Corporation::loadByAcctIdAndSourceId(
                    $account->getAccountId(),
                    \Genesis_Entity_Source::ID_MANUAL
                );

                if (!$corp) {
                    $corp = new \Genesis_Entity_ManualCorporation();
                    $corp->setAccountId($account->getAccountId());
                    $corp->setCreated(date('Y-m-d H:i:s', time()));
                    $corp->setCorpname($account->getName());
                    $corp->setSourceId(\Genesis_Entity_Source::ID_MANUAL);
                    $corp = \Genesis_Service_Corporation::save($corp);
                }

                $facility = new \Genesis_Entity_Facility();
                $facility->setTitle($facilityName);
                $facility->setCorporationId($corp->getId());
                $facility->setSourceId($corp->getSourceId());
                $facility->setActive(0);
                $facility->setPublished(1);
                $facility->setApproved(1);
                $facility->setPhone($request->request->get('phone'));

                $address1 = $request->request->get('address1');
                $city = $request->request->get('city');
                $state = $request->request->get('state');
                $zip = $request->request->get('zip');

                $location = \Genesis_Service_Location::loadByAddress($address1, $city, $state, $zip);

                // Does this location already exist?
                if (!$location) {
                    // Call geocoder
                    $fullAddress = $request->request->get('address').' '.$city.' '.$state.' '.$zip;
                    $location = \Genesis_Service_Location::geoCodePhysicalAddress($fullAddress);
                    $location = \Genesis_Service_Location::save($location);
                }

                $facility->setLocationId($location->getId());
                \Genesis_Service_Facility::save($facility, $user);
            }

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new JsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/same-co-address
     * http://localhost:9019/signup-end/same-co-address
     */
    #[Route('/signup-end/same-co-address', name: 'signup_end_same_co_address', methods: ['POST'])]
    public function sameCoAddressAction(Request $request): JsonResponse
    {
        try {
            $user = $this->getLoggedUser();
            $account = \Genesis_Service_Account::loadById($user->getAccountId());

            if ($account && $account->getLocation()) {
                $address = [
                    'name' => $account->getName(),
                    'addr' => $account->getLocation()->getAddress1(),
                    'city' => $account->getLocation()->getCity(),
                    'state' => $account->getLocation()->getState(),
                    'zip' => $account->getLocation()->getZip(),
                    'phone' => $user->getPhone(),
                ];

                return new JsonResponse($address);
            } else {
                return new JsonResponse(['success' => false, 'message' => 'No corporate address on file.']);
            }
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new JsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}
