<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Account;
use Sparefoot\MyFootService\Service\Unit;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiAccountController extends ApiBaseController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/accounts/{ACCOUNT_ID}
     * http://localhost:9019/api/accounts/{ACCOUNT_ID}
     */
    #[Route('/api/accounts/{account_id}{slash}', name: 'api_accounts_show', requirements: ['slash' => '/?', 'account_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        if (!$request->isMethod('GET')) {
            throw new ApiException(ApiException::NOT_IMPLEMENTED);
        }

        $accountId = $request->get('account_id');
        if ($accountId) {
            try {
                $account = Account::validateAccountId($accountId);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            try {
                User::validateAccountAccess($account);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }
            $results = [
                'meta' => [
                    'count' => 1,
                ],
                'data' => Account::toArray($account),
                'links' => [],
            ];

            return $this->json($results);
        }

        // if user is not logged in, not allowed to get all
        $user = $this->getLoggedUser();
        if ($user && $user instanceof \Genesis_Service_UserAccess && !call_user_func([$user, 'isMyFootGod'])) {
            throw new ApiException(ApiException::FORBIDDEN);
        }

        $accounts = \Genesis_Dao_Account::selectBriefAccountsForMenu();
        $accountsArray = is_array($accounts) ? $accounts : [];
        $results = [
            'meta' => [
                'count' => count($accountsArray),
            ],
            'data' => $accountsArray,
            'links' => [],
        ];

        return $this->json($results);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/accounts/{ACCOUNT_ID}/specials
     * http://localhost:9019/api/accounts/{ACCOUNT_ID}/specials
     */
    #[Route('/api/accounts/{account_id}/specials{slash}', name: 'api_accounts_specials', requirements: ['slash' => '/?', 'account_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET'])]
    public function specialsAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        if (!$request->isMethod('GET')) {
            throw new ApiException(ApiException::NOT_IMPLEMENTED);
        }

        $response = [];
        $accountId = $request->get('account_id');
        $type = $request->get('type');

        try {
            $account = Account::validateAccountId($accountId);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }

        try {
            User::validateAccountAccess($account);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
        }

        $accountSpecials = \Genesis_Service_Special::loadByAccountId($account->getId());
        foreach ($accountSpecials as $special) {
            if ($special->getDefault()) {
                continue;
            }
            switch ($type) {
                case 'discount':
                    if ($special->isDiscount()) {
                        $response[] = Unit::serializeUnitSpecial($special);
                    }
                    break;
                case 'promo':
                    if ($special->isPromo() && $type != 'discount') {
                        $response[] = Unit::serializeUnitSpecial($special);
                    }
                    break;
                default:
                    $response[] = Unit::serializeUnitSpecial($special);
            }
        }

        return $this->json(['data' => $response]);
    }
}
