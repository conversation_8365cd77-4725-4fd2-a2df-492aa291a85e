<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class InsightsController extends AbstractRestrictedController
{
    /**
     * @var \SoapClient
     */
    private $soap;

    protected function _init(): void
    {
        $domain = \Genesis_Config_Server::getEnvDomain();
        if ($domain === 'localhost:8888') { // meh
            $domain = 'localhost';
        }

        $this->soap = new \SoapClient(
            'http://pita.sparefoot.'.$domain.'/quicksoap?wsdl',
            ['cache_wsdl' => WSDL_CACHE_NONE]
        );
        $this->view->user = $this->getLoggedUser();
        $this->view->accountId = $this->getLoggedUser()->getAccountId();
        $this->view->allReports = $this->soap->getReports($this->getLoggedUser()->getAccount()->getSfAccountId());
    }

    // get report parameters and display option to view as HTML table or download
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/custom
     * http://localhost:9019/insights/custom
     */
    #[Route('/insights/custom', name: 'insights_custom', methods: ['GET', 'POST'])]
    public function customAction(Request $request): Response
    {
        $reportClassName = $request->get('report');
        $account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
        $this->view->inputs = $this->soap->getReportParameters($reportClassName, $account->getSfAccountId());
        $this->view->reportClassName = $reportClassName;
        $this->view->reportName = $this->soap->getReportName($reportClassName);

        if ($request->isMethod('GET')) {
            if ($request->get('csv') === 'Download') {
                $csvContent = $this->soap->renderReport($reportClassName, $request->query->all(), $account->getSfAccountId());

                return new Response(
                    $csvContent,
                    Response::HTTP_OK,
                    [
                        'Content-Type' => 'text/csv',
                        'Content-Disposition' => 'attachment; filename="report.csv"',
                    ]
                );
            } else {
                $this->view->renderedTable = $this->soap->renderReportTable($reportClassName, $request->query->all(), $account->getSfAccountId());
            }
        }

        $this->view->scripts = ['insights/custom'];

        return $this->render('insights/custom.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/customtable
     * http://localhost:9019/insights/customtable
     */
    #[Route('/insights/customtable', name: 'insights_customtable', methods: ['GET', 'POST'])]
    public function customtableAction(Request $request): Response
    {
        $account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
        $tableContent = $this->soap->renderReportTable($request->get('report'), $request->query->all(), $account->getSfAccountId());

        return new Response($tableContent);
    }

    // download as CSV file
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/download
     * http://localhost:9019/insights/download
     */
    #[Route('/insights/download', name: 'insights_download', methods: ['GET', 'POST'])]
    public function downloadAction(Request $request): Response
    {
        $csvContent = $this->soap->renderReport($request->get('report'), $request->query->all());

        return new Response(
            $csvContent,
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="report.csv"',
            ]
        );
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights
     * http://localhost:9019/insights
     */
    #[Route('/insights', name: 'insights_index', methods: ['GET', 'POST'])]
    public function indexAction(Request $request): Response
    {
        $this->view->scripts = ['insights/index'];
        $this->view->title = 'Pricing Data';

        $facilities = $this->view->user->getManagableFacilities();
        $show_report = false;
        $reports_count = 0;
        $str_report_row = '';
        $facilityIds = [];
        $hasInsightsFacNoneEnabled = $hasInsightsFacNoData = $hasInsightsFacNoBe = $stillProcessingReport = false;
        $dayOfMonth = date('j');

        $reportDate = $request->get('report_date');
        if (!empty($reportDate)) {
            $insights_date = $reportDate;
        } elseif ($dayOfMonth < \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY) {
            $insights_date = date('Y-m-'.\Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY, strtotime('-1 month'));
        } else {
            $insights_date = date('Y-m-'.\Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY);
        }

        if ($dayOfMonth < \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY) {
            $this->view->nextReport = date('F Y');
            $this->view->nextReportDays = \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY - $dayOfMonth;
        } else {
            $this->view->nextReport = date('F Y', strtotime('+1 month'));
            $this->view->nextReportDays = date('t') - $dayOfMonth + \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY;
        }

        // TODO: Complete migration of complex insights processing logic
        // The original Zend implementation had complex facility processing that needs careful review

        $this->view->show_report = $show_report;
        $this->view->reports_count = $reports_count;
        $this->view->str_report_row = $str_report_row;
        $this->view->facilityIds = implode(',', $facilityIds);
        $this->view->hasInsightsFacNoneEnabled = $hasInsightsFacNoneEnabled;
        $this->view->hasInsightsFacNoData = $hasInsightsFacNoData;
        $this->view->hasInsightsFacNoBe = $hasInsightsFacNoBe;
        $this->view->stillProcessingReport = $stillProcessingReport;
        $this->view->insights_date = $insights_date;

        return $this->render('insights/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/generatenew
     * http://localhost:9019/insights/generatenew
     */
    #[Route('/insights/generatenew', name: 'insights_generatenew', methods: ['POST'])]
    public function generatenewAction(Request $request): JsonResponse
    {
        $accountId = $request->get('account_id');
        $facilities = \Genesis_Service_Facility::loadByAccountId($accountId);
        $facilities->setRewindable(false);

        $success = 0;
        // $searchClient = new \AccountMgmt_Clients_SearchClient('Myfoot-Insights', false);
        foreach ($facilities as $facility) {
            try {
                $facility->generateInsightsReport(false, \Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL, null);
                $facility->generateInsightsReport(false, \Genesis_Entity_Insights::REPORT_TYPE_LOCAL, null);
                $success = 1;
            } catch (\Exception $e) {
                continue;
            }
        }

        return new JsonResponse([
            'success' => $success,
        ]);
    }

    // list available custom reports
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/reports
     * http://localhost:9019/insights/reports
     */
    #[Route('/insights/reports', name: 'insights_reports', methods: ['GET'])]
    public function reportsAction(Request $request): Response
    {
        return $this->render('insights/reports.html.twig', [
            'view' => $this->view,
        ]);
    }

    // render as HTML table
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/table
     * http://localhost:9019/insights/table
     */
    #[Route('/insights/table', name: 'insights_table', methods: ['GET', 'POST'])]
    public function tableAction(Request $request): Response
    {
        $params = $request->query->all();
        $reportName = $params['report'];
        $renderedTable = $this->soap->renderReportTable($reportName, $params);

        return new Response($renderedTable);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/tellme
     * http://localhost:9019/insights/tellme
     */
    #[Route('/insights/tellme', name: 'insights_tellme', methods: ['POST'])]
    public function tellmeAction(Request $request): Response
    {
        $msg = new \Genesis_Entity_EmailMessage();
        $msg->setSubject('Insights product information requested');

        $msg->setBody(
            'First Name: '.$this->view->user->getFirstName().'<br/>'.
            'Last Name: '.$this->view->user->getLastName().'<br/><br/>'.
            'Phone: '.$this->view->user->getPhone().'<br/>'.
            'Email: '.$this->view->user->getEmail().'<br/><br/>'.
            'User Id: '.$this->view->user->getId().'<br/>'.
            'Member Since: '.$this->view->user->getMemberSince().'<br/>'
        );

        \Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $msg, []);

        return new Response('Email Sent');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/terms
     * http://localhost:9019/insights/terms
     */
    #[Route('/insights/terms', name: 'insights_terms', methods: ['GET', 'POST'])]
    public function termsAction(Request $request): Response
    {
        $acctMgmtUser = \Genesis_Service_UserAccess::loadById($this->view->user->getId());
        // $acctMgmtUser->setTermsVersion(\Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);
        \Genesis_Service_UserAccess::save($acctMgmtUser);

        // $this->getLoggedUser()->setTermsVersion(\Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);

        $this->view->scripts = ['insights/terms'];

        return $this->render('insights/terms.html.twig', [
            'view' => $this->view,
        ]);
    }
}
