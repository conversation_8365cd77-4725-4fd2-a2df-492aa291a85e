<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SettingsController extends AbstractRestrictedController
{
    /**
     * Settings index - Redirects to home settings page.
     *
     * GET /settings - Shows settings home
     * Sample:
     * https://myfoot.sparefoot.com/settings
     * http://localhost:9019/settings
     */
    #[Route('/settings', name: 'settings_index', methods: ['GET'])]
    public function indexAction(Request $request): Response
    {
        return $this->homeAction($request);
    }

    /**
     * Settings home - Main settings dashboard.
     *
     * GET /settings/home - Shows settings dashboard
     * Sample:
     * https://myfoot.sparefoot.com/settings/home
     * http://localhost:9019/settings/home
     */
    #[Route('/settings/home', name: 'settings_home', methods: ['GET'])]
    public function homeAction(Request $request): Response
    {
        return $this->render('settings/home.html.twig');
    }

    /**
     * My account settings - User profile and password management.
     *
     * GET /settings/myaccount - Show account form
     * POST /settings/myaccount - Update account details
     * Sample:
     * https://myfoot.sparefoot.com/settings/myaccount
     * http://localhost:9019/settings/myaccount
     */
    #[Route('/settings/myaccount', name: 'settings_myaccount', methods: ['GET', 'POST'])]
    public function myaccountAction(Request $request): Response
    {
        $user = $this->getLoggedUser();
        $account = $user->getAccount();
        $location = $account->getLocation();

        $templateData = [
            'title' => 'Your Account - Settings',
            'user' => $user,
            'account' => $account,
            'zip' => $location ? $location->getZip() : '',
            'city' => $location ? $location->getCity() : '',
            'address' => $location ? $location->getAddress1() : '',
            'state' => $location ? $location->getState() : '',
            'erroredFields' => [],
        ];

        if ($request->isMethod('POST')) {
            try {
                // Validate and update user data
                $firstName = $request->request->get('fname');
                if (!$firstName || strlen($firstName) === 0) {
                    $templateData['erroredFields'] = ['fname'];
                    throw new \Exception('Please enter a first name.');
                }
                $user->setFirstName($firstName);

                $lastName = $request->request->get('lname');
                if (!$lastName || strlen($lastName) === 0) {
                    $templateData['erroredFields'] = ['lname'];
                    throw new \Exception('Please enter a last name.');
                }
                $user->setLastName($lastName);

                $email = $request->request->get('email');
                if (!$email || strlen($email) === 0) {
                    $templateData['erroredFields'] = ['email'];
                    throw new \Exception('Please enter an email address');
                }
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $templateData['erroredFields'] = ['email'];
                    throw new \Exception('Please enter a valid email address.');
                }
                $user->setEmail($email);

                $phone = $request->request->get('phone');
                if ($phone && strlen($phone) > 0) {
                    $cleanPhone = preg_replace('/[^0-9]/', '', $phone);
                    if (!preg_match('/[0-9]{7,14}/', $cleanPhone)) {
                        $templateData['erroredFields'] = ['phone'];
                        throw new \Exception('Phone number is invalid');
                    }
                    $user->setPhone($cleanPhone);
                }

                $aboutMe = $request->request->get('aboutme');
                if ($aboutMe && strlen($aboutMe) > 0) {
                    $user->setAboutMe($aboutMe);
                }

                \Genesis_Service_User::save($user);

                // Handle password update
                $oldPassword = $request->request->get('old_password');
                if ($oldPassword && strlen($oldPassword) > 0) {
                    $newPassword = $request->request->get('new_password');
                    $newPasswordConfirm = $request->request->get('new_password_confirm');

                    if (!$user->checkRawPassword($oldPassword)) {
                        $templateData['erroredFields'] = ['old_password'];
                        throw new \Exception('Current password is incorrect.');
                    }

                    if ($newPassword !== $newPasswordConfirm) {
                        $templateData['erroredFields'] = ['new_password', 'new_password_confirm'];
                        throw new \Exception('Passwords do not match.');
                    }

                    \Genesis_Service_User::updatePassword($this->getLoggedUser(), $newPassword);
                    $this->getLoggedUser()->setRawPassword($newPassword);
                }

                $templateData['user'] = $this->getLoggedUser();
                $templateData['alert'] = 'Changes saved.';
                $templateData['alertClass'] = 'alert-success';
            } catch (\Exception $e) {
                $templateData['alert'] = '<strong>Error</strong>: '.$e->getMessage();
                $templateData['alertClass'] = 'alert-danger';
            }
        }

        return $this->render('settings/myaccount.html.twig', $templateData);
    }

    /**
     * Corporate account settings - Company information management (Admin only).
     *
     * GET /settings/corporate - Show corporate form
     * POST /settings/corporate - Update corporate details
     * Sample:
     * https://myfoot.sparefoot.com/settings/corporate
     * http://localhost:9019/settings/corporate
     */
    #[Route('/settings/corporate', name: 'settings_corporate', methods: ['GET', 'POST'])]
    public function corporateAction(Request $request): Response
    {
        $user = $this->getLoggedUser();
        $role = $user->getMyfootRole();

        // Check admin permissions
        if (!in_array($role, [
            \Genesis_Entity_UserAccess::ROLE_GOD,
            \Genesis_Entity_UserAccess::ROLE_ADMIN,
        ])) {
            return $this->redirectToRoute('settings_myaccount');
        }

        $account = $user->getAccount();

        $templateData = [
            'title' => 'Corporate Account - Settings',
            'user' => $user,
            'account' => $account,
            'erroredFields' => [],
            'terms_link' => '/document/terms',
        ];

        // Check for terms sheet envelope
        $displayTermsDownload = false;
        if ($account->getTermsEnvelopeId() !== null && $account->getTermsEnvelopeId() !== '') {
            $displayTermsDownload = true;
        }
        $templateData['display_terms_download'] = $displayTermsDownload;

        // Add terms addendum link if available
        if ($account->getTermsAddendum()) {
            $templateData['termsAddendumLink'] = '/document/terms-addendum';
        }

        if ($request->isMethod('POST')) {
            try {
                $address = $request->request->get('address');
                if (!$address || strlen($address) === 0) {
                    throw new \Exception('Please enter an address.');
                }

                $city = $request->request->get('city');
                if (!$city || strlen($city) === 0) {
                    throw new \Exception('Please enter a city.');
                }

                $zip = $request->request->get('zip');
                if (!$zip || strlen($zip) === 0) {
                    throw new \Exception('Please enter a zip code.');
                }

                $companyName = $request->request->get('company_name');
                if (!$companyName || strlen($companyName) === 0) {
                    throw new \Exception('Please enter a company name.');
                }

                $state = $request->request->get('state');
                $account->setName($companyName);

                // Geocode and save location
                $fullAddress = $address.' '.$city.' '.$state.' '.$zip;
                $location = \Genesis_Service_Location::geoCodePhysicalAddress($fullAddress, $address, $zip);
                $location = \Genesis_Service_Location::save($location);

                $account->setLocationId($location->getId());
                \Genesis_Service_Account::save($account);

                // Requery the Account and set it
                $account = $this->getLoggedUser()->getAccount();
                $templateData['account'] = $account;

                $templateData['alert'] = 'Changes saved.';
                $templateData['alertClass'] = 'alert-success';
            } catch (\Exception $e) {
                $templateData['alert'] = '<strong>Error</strong>: '.$e->getMessage();
                $templateData['alertClass'] = 'alert-danger';
            }
        }

        return $this->render('settings/corporate.html.twig', $templateData);
    }

    /**
     * Update facility list for user - AJAX endpoint for facility management.
     *
     * POST /settings/update-facility-list - Update facility permissions
     * Body: {userid: 123}
     * Sample:
     * https://myfoot.sparefoot.com/settings/update-facility-list
     * http://localhost:9019/settings/update-facility-list
     */
    #[Route('/settings/update-facility-list', name: 'settings_update_facility_list', methods: ['POST'])]
    public function updatefacilitylistAction(Request $request): Response
    {
        $userId = $request->request->get('userid');

        if (!$userId) {
            return new Response('(select a user first)');
        }

        $acctMgmtUser = \Genesis_Service_UserAccess::loadById($userId);

        // Get manageable facilities and save IDs in an array
        $restriction = \Genesis_Db_Restriction::equal('published', 1);
        $restriction->setOrder(\Genesis_Db_Order::asc('title'));
        $managableFacs = $acctMgmtUser->getManagableFacilities($restriction);

        $managableArray = [];
        foreach ($managableFacs as $managableFac) {
            $managableArray[] = $managableFac->getId();
        }

        $facilities = \Genesis_Service_Facility::loadByAccountId(
            $this->getLoggedUser()->getAccountId(),
            $restriction
        );

        // Build facility list HTML
        $output = '<ul><li><label for="all"><input type="checkbox" id="all" onclick="checkAllFacilities();"/>SELECT ALL</label></li><hr/>';

        foreach ($facilities as $fac) {
            $checked = in_array($fac->getId(), $managableArray) ? 'checked="checked"' : '';
            $hiddenText = !$fac->getActive() ? ' [Hidden]' : '';

            $output .= '<li><label for="'.$fac->getId().'">';
            $output .= '<input type="checkbox" id="'.$fac->getId().'" name="fac_id" value="'.$fac->getId().'" '.$checked.'>';
            $output .= $fac->getTitle().$hiddenText.'</label></li>';
        }

        $output .= '</ul>';

        return new Response($output);
    }

    /**
     * Billing settings - Payment and billing information.
     *
     * GET /settings/billing - Show billing information
     * Sample:
     * https://myfoot.sparefoot.com/settings/billing
     * http://localhost:9019/settings/billing
     */
    #[Route('/settings/billing', name: 'settings_billing', methods: ['GET'])]
    public function billingAction(Request $request): Response
    {
        return $this->render('settings/billing.html.twig', [
            'account' => $this->getLoggedUser()->getAccount(),
        ]);
    }

    /**
     * Insert account software integration - AJAX endpoint for software integration.
     *
     * POST /settings/insert-account-software - Add software integration
     * Body: {integration_type: [], software_account_id: 123, software_user_id: 456}
     * Sample:
     * https://myfoot.sparefoot.com/settings/insert-account-software
     * http://localhost:9019/settings/insert-account-software
     */
    #[Route('/settings/insert-account-software', name: 'settings_insert_account_software', methods: ['POST'])]
    public function insertAccountSoftwareAction(Request $request): Response
    {
        $integrationType = $request->request->get('integration_type');
        $softwareAccountId = $request->request->get('software_account_id');
        $softwareUserId = $request->request->get('software_user_id');

        if (!$integrationType || !$softwareAccountId) {
            return new Response('Error! Please select a software');
        }

        if (is_array($integrationType)) {
            foreach ($integrationType as $key => $val) {
                $software = new \Genesis_Entity_AccountSoftware();
                $software->setAccountId($softwareAccountId);
                $software->setSourceId($val);
                $software->setUserId($softwareUserId);
                \Genesis_Service_AccountSoftware::save($software);
            }
        }

        return new Response('Success');
    }
}
