<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Controller\AbstractView\GenericView;
use Symfony\Component\HttpFoundation\Request;

/**
 * Abstract Public Controller.
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 * Migrated to Symfony by Augment Agent
 */
abstract class AbstractPublicController extends AbstractCommonController
{
    protected $view;

    /**
     * Migrated from final public function init()
     * This is called in ControllerInitSubscriber.
     */
    public function init(Request $request): mixed
    {
        $this->setRequest($request);
        // we can set View in initBeforeControllerAction
        if (empty($this->view)) {
            $this->view = new GenericView();
        }
        $this->_init();

        $this->view->errorMessages = [];
        $this->view->successMessages = [];

        return null;
    }

    /**
     * Initialize UI state for public pages.
     */
    private function _initPublicUiState(Request $request): void
    {
        // Set basic view properties for public pages
        $this->view->selectedTab = $this->getTab();

        if ($request->get('welcome')) {
            $this->view->welcomeMessage = true;
        }
    }

    /**
     * Get the current tab for navigation - override in child controllers.
     */
    protected function getTab(): string
    {
        return '';
    }

    /**
     * Dispatch error messages for public pages.
     */
    protected function dispatchError($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->errorMessages)) {
            $this->view->errorMessages[] = $messages;
        } else {
            $this->view->errorMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('error', $messages);
    }

    /**
     * Dispatch success messages for public pages.
     */
    protected function dispatchSuccess($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->successMessages)) {
            $this->view->successMessages[] = $messages;
        } else {
            $this->view->successMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('success', $messages);
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     * In Symfony, this is typically handled by the Request object and form validation.
     */
    public function getParam(Request $request, string $paramName, $default = null)
    {
        $param = $request->get($paramName, $default);

        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Helper method to extract controller name from Symfony route.
     */
    protected function getControllerNameFromRoute(?string $route): string
    {
        if (!$route) {
            return '';
        }

        // Extract controller name from route pattern
        // This is a simplified approach - you may need to adjust based on your routing patterns
        $parts = explode('_', $route);

        return $parts[0] ?? '';
    }
}
