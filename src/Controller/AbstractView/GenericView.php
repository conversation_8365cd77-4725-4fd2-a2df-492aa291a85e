<?php

namespace Sparefoot\MyFootService\Controller\AbstractView;

use Sparefoot\MyFootService\Models\Features;

/**
 * View class for handling the Account list action.
 */
#[\AllowDynamicProperties]
class GenericView extends \stdClass
{
    private $genesisService;

    public function get_class(mixed $classInstance): string
    {
        return get_class($classInstance);
    }

    public function implode(array $array, string $glue): string
    {
        return implode($glue, $array);
    }

    // Genesis_Util_Versioner::version('/css/bootstrap.min.css')
    public function genesisUtilVersionerVersion(string $path): string
    {
        return \Genesis_Util_Versioner::version($path);
    }

    // strtotime
    public function strtotime(string $time): int
    {
        return strtotime($time);
    }

    // new DateTime
    public function newDateTime(string $time): \DateTime
    {
        return new \DateTime($time);
    }

    public function formatEstimatedMoney($amt): string
    {
        return number_format(round($amt, -2), 2);
    }

    // Genesis_Util_Formatter::formatDateDiff
    public function genesisUtilFormatterFormatDateDiff(\DateTime $date1, \DateTime $date2): string
    {
        return \Genesis_Util_Formatter::formatDateDiff($date1, $date2);
    }

    // $queryName = 'Sparefoot\\PitaService\\Report\\'.$this->_className.'\\Query';
    //   $query = new $queryName();
    // $this->view->query->{$metricInfo['getter']}()
    /**
     * Get the metric info getter value.
     *
     * @param mixed $query      The query object. Example: new Sparefoot\PitaService\Report\Account\Query();
     * @param mixed $metricInfo the metric info, which can be an array or a string
     *
     * @return mixed the value returned by the getter method or the metric info itself
     */
    public function getMetricInfoGetter($query, $metricInfo): mixed
    {
        if (is_array($metricInfo)) {
            return $query->{$metricInfo['getter']}();
        }

        return null;
    }

    // Genesis_Config_Server::isProduction()
    public function genesisConfigServerIsProduction(): bool
    {
        return \Genesis_Config_Server::isProduction();
    }

    // Genesis_Entity_Site::COMMISSION_PERCENT
    public function genesisEntitySiteCommission(string $key): string
    {
        $list = [
            'Genesis_Entity_Site::COMMISSION_PERCENT' => \Genesis_Entity_Site::COMMISSION_PERCENT,
            'Genesis_Entity_Site::COMMISSION_FLAT' => \Genesis_Entity_Site::COMMISSION_FLAT,
        ];

        return $list[$key] ?? '';
    }

    /**
     * Returns the value of a Genesis constant specified by name.
     *
     * @param string $constantName the name of the constant to return, in the format 'Class::CONSTANT_NAME'
     *
     * @return mixed The value of the specified constant.
     *               Example usage: in Twig template, you can use:
     *               {{ view.genesisConst('Genesis_Entity_Site::COMMISSION_PERCENT') }}
     */
    public function genesisConst(string $constantName): mixed
    {
        $parts = explode('::', $constantName);
        if (count($parts) !== 2) {
            return null;
        }

        $class = $parts[0];
        $const = $parts[1];

        if (defined("\\$class::$const")) {
            return constant("\\$class::$const");
        }

        return null;
    }

    public function dateFormat(string $date, string $format = 'Y-m-d H:i:s'): string
    {
        return date_format(new \DateTime($date), $format);
    }

    public function getEnv(string $key): mixed
    {
        return getenv($key) ?: null;
    }

    // str_replace("\0", "--NULL--", serialize($this->query))
    public function serializeQuery($query): string
    {
        return str_replace("\0", '--NULL--', serialize($query));
    }

    public function CDN($path)
    {
        return \Genesis_Util_Versioner::version($path);
    }

    public function getHostName()
    {
        return gethostname();
    }

    /**
     * Generate body class names based on view properties.
     *
     * @return string Space-separated class names for the body element
     */
    public function getBodyClassNames(): string
    {
        $bodyClassNames = [];

        if (isset($this->banner) && count($this->banner) > 0) {
            $bodyClassNames[] = 'banner-showing';
        }

        return implode(' ', $bodyClassNames);
    }

    /**
     * Get the current request URI.
     *
     * @return string The current request URI
     */
    public function getRequestUri(): string
    {
        return $_SERVER['REQUEST_URI'] ?? '';
    }

    public function setLoggedUser(mixed $user): void
    {
        $this->loggedUser = $user;
    }

    public function getLoggedUser(): mixed
    {
        return $this->loggedUser;
    }

    public function getActionName(): string
    {
        return $this->actionName;
    }

    public function setActionName(string $actionName): void
    {
        $this->actionName = $actionName;
    }

    public function setRouteName(string $routeName): void
    {
        $this->routeName = $routeName;
    }

    public function getRouteName(): mixed
    {
        return $this->routeName;
    }

    public function setAccountId(mixed $accountId): void
    {
        $this->accountId = $accountId;
    }

    public function getAccountId(): mixed
    {
        return $this->accountId;
    }

    public function setFacilityId(mixed $facilityId): void
    {
        $this->facilityId = $facilityId;
    }

    public function getFacilityId(): mixed
    {
        return $this->facilityId;
    }

    public function setScripts(array $scripts): void
    {
        $this->scripts = $scripts;
    }

    public function getScripts(): array
    {
        return $this->scripts ?? [];
    }

    public function isProduction(): bool
    {
        return \Genesis_Config_Server::isProduction();
    }

    public function isActiveSALESFORCE_PARDOT(): bool
    {
        return \Genesis_Service_Feature::isActive(Features::SALESFORCE_PARDOT);
    }

    public function isActiveQUALAROO_HOOK(): bool
    {
        return \Genesis_Service_Feature::isActive(Features::QUALAROO_HOOK);
    }

    // SESSION_CAM_RECORDER
    public function isActiveSESSION_CAM_RECORDER(): bool
    {
        return \Genesis_Service_Feature::isActive(Features::SESSION_CAM_RECORDER);
    }

    // Genesis_Config_Server::getEnvironmentAsString()
    public function getEnvironmentString(): string
    {
        return \Genesis_Config_Server::getEnvironmentAsString();
    }
}
