<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Customers Controller.
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR> by Augment Agent
 */
class CustomersController extends AbstractRestrictedController
{
    /**
     * Initialize controller - migrated from _init().
     */
    protected function _init(): void
    {
        // Initialize any controller-specific data here
        $this->view->title = 'Customers';
    }

    /**
     * Customers Index Action - displays customer reservations.
     */
    #[Route('/customers', name: 'customers_index', methods: ['GET'])]
    public function indexAction(Request $request): Response
    {
        // Get the action parameter, default to 'reservations'
        $action = $request->query->get('action', 'reservations');

        // Get facility ID from session or request
        $facilityId = $this->getSession()->get('facilityId') ?: $request->query->get('fid');

        if (!$facilityId) {
            throw new \Exception('Facility ID is required to view customers');
        }

        // Set view data
        $this->view->facilityId = $facilityId;
        $this->view->action = $action;
        $this->view->facility = \Genesis_Service_Facility::loadById($facilityId);

        // Load customer data based on action
        switch ($action) {
            case 'reservations':
                $this->loadReservations();
                break;
            case 'moveins':
                $this->loadMoveIns();
                break;
            default:
                $this->loadReservations();
                break;
        }

        // Set scripts for the page
        $this->view->scripts = [
            'customers/index',
        ];

        return $this->render('customers/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Load reservations data.
     */
    private function loadReservations(): void
    {
        // TODO: Implement reservation loading logic
        // This would typically load reservation data from Genesis services
        $this->view->reservations = [];
        $this->view->pageTitle = 'Customer Reservations';
    }

    /**
     * Load move-ins data.
     */
    private function loadMoveIns(): void
    {
        // TODO: Implement move-ins loading logic
        // This would typically load move-in data from Genesis services
        $this->view->moveIns = [];
        $this->view->pageTitle = 'Customer Move-Ins';
    }

    /**
     * Get the current tab for navigation.
     */
    protected function getTab(): string
    {
        return parent::TAB_CUSTOMERS;
    }
}
