<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class LoginController extends AbstractPublicController
{
    protected $view;

    #[Route('/login', name: 'login_index')]
    public function index(Request $request, AuthenticationUtils $authenticationUtils): Response
    {
        if ($this->getUser()) {
            // If the user is already logged in, redirect to the homepage or dashboard
            return $this->redirectToRoute('inventory_index');
        }

        $session = $request->getSession();
        // $session->set('test_key', 'test_value');

        $this->view = new \stdClass();
        // Get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();

        if (empty($error) && !empty($session->get('security.authentication_error', null))) {
            $error = $session->get('security.authentication_error', null)->getMessage();
            // clear "security.authentication_error" session variable
            $session->remove('security.authentication_error');
        }
        // Last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        if (\Genesis_Config_Server::isProduction()) {
            $envClass = 'prod';
        } elseif (\Genesis_Config_Server::isStaging()) {
            $envClass = 'stg';
        } elseif (\Genesis_Config_Server::isDev()) {
            $envClass = 'dev';
        } else {
            $envClass = 'local';
        }
        $this->view->envClass = $envClass;
        $this->view->sessionId = $session->getId();
        $this->view->error = $error ?? '';
        $this->view->last_username = $lastUsername ?? '';

        return $this->render('login/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    #[Route('/login/check', name: 'login_check', methods: ['POST'])]
    public function check(): void
    {
        // This method will not be executed,
        // as the route is handled by the Security system
        // it checks the credentials through CustomAuthenticator
        throw new \LogicException('This code should not be reached! check() in LoginController');
    }

    #[Route('/login/currentuser', name: 'login_currentuser')]
    public function getCurrentUser(): JsonResponse
    {
        $user = $this->getUser();

        if (!$user) {
            return $this->json([
                'message' => 'No user is logged in',
            ], Response::HTTP_UNAUTHORIZED);
        }

        return $this->json([
            'username' => $user->getUserIdentifier(),
            'roles' => $user->getRoles(),
        ]);
    }

    // #[Route('/logout', name: 'login_logout', methods: ['GET'])]
    #[Route('/logout', name: 'loign_logout', methods: ['GET'])]
    #[Route('/login/logout', name: 'login_loign_logout', methods: ['GET'])]
    public function logout(): Response
    {
        return $this->redirect('/logout');
        // This method can be blank - it will be intercepted by the logout key in security.yaml
    }

    #[Route('/login/am_i_logged_in', name: 'login_am_i_logged_in')]
    public function amILoggedIn(): JsonResponse
    {
        return new JsonResponse(['loggedIn' => $this->getUser() !== null]);
    }

    #[Route('/login/is_user_god', name: 'login_is_user_god')]
    public function isUserGod(): JsonResponse
    {
        return new JsonResponse(['isGod' => $this->isGranted('ROLE_GOD')]);
    }

    #[Route('/login/reset_password', name: 'login_reset_password', methods: ['POST'])]
    public function resetPassword(Request $request): Response
    {
        return new Response('Password reset functionality is not implemented yet.', Response::HTTP_NOT_IMPLEMENTED);
    }
}
