<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\FormValidationException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AccountsController extends AbstractRestrictedController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/accounts
     * http://localhost:9019/accounts
     */
    #[Route('/accounts', name: 'accounts_index', methods: ['GET'])]
    public function indexAction(Request $request): Response
    {
        $this->view->accounts = \Genesis_Dao_Account::selectBriefAccountsForMenu();

        return $this->render('accounts/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/accounts/download-terms
     * http://localhost:9019/accounts/download-terms
     */
    #[Route('/accounts/download-terms', name: 'accounts_download_terms', methods: ['GET'])]
    public function downloadTermsAction(Request $request): Response
    {
        $tosData = $this->getLoggedUser()->getAccount()->retrieveTOSData();

        $response = new Response();
        $response->headers->set('Content-Disposition', 'attachment; filename="Sparefoot_terms.pdf"');
        $response->headers->set('Content-Length', $tosData->BodyLength);
        $response->headers->set('Content-Type', $tosData->ContentType);
        $response->setContent($tosData->Body);

        return $response;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/accounts/update-terms
     * http://localhost:9019/accounts/update-terms
     */
    #[Route('/accounts/update-terms', name: 'accounts_update_terms', methods: ['POST'])]
    public function updatetermsAction(Request $request): JsonResponse
    {
        try {
            $termsVersion = $request->get('terms_version', \Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);
            $agreed = $request->get('agreed', false);

            if ($agreed !== 'agreed') {
                throw new FormValidationException('Must agree to the new Terms of Service.');
            }

            if ($this->getLoggedUser()->getMyfootRole() === \Genesis_Entity_UserAccess::ROLE_GOD
                && !$this->getLoggedUser()->getAccount()->getTestAccount()
            ) {
                throw new FormValidationException('You cannot agree to the terms on this account.');
            }

            // Save terms version info
            $account = $this->getLoggedUser()->getAccount();
            $account->setTermsVersion($termsVersion);
            $account->setTermsAgreedByUserId($this->getLoggedUser()->getId());
            $account->setTermsAgreedDate(date('Y-m-d H:i:s'));
            \Genesis_Service_Account::save($account);

            $response = ['success' => true];
        } catch (FormValidationException $e) {
            return new JsonResponse(['formError' => $e->getMessage()], 400);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 400);
        }

        return new JsonResponse($response);
    }
}
