<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ErrorController extends AbstractPublicController
{
    /**
     * Error action - handles application errors and exceptions.
     *
     * Sample:
     * https://myfoot.sparefoot.com/error
     * http://localhost:9019/error
     */
    #[Route('/error', name: 'error_index', methods: ['GET', 'POST'])]
    public function errorAction(Request $request): Response
    {
        $acceptType = $request->headers->get('Accept', '');
        $statusCode = $request->query->getInt('status', 500);
        $errorType = $request->query->get('type', 'unknown');
        $errorMessage = $request->query->get('message', 'An error occurred');
        $exception = $request->attributes->get('exception');

        // Initialize view
        if (empty($this->view)) {
            $this->view = new \stdClass();
        }

        // Determine error type and status code
        switch ($errorType) {
            case 'controller_not_found':
            case 'action_not_found':
            case '404':
                $statusCode = 404;
                $this->view->message = 'Page not found';
                break;
            default:
                $statusCode = $statusCode ?: 500;
                $this->view->message = $errorMessage ?: 'Application error';

                // Log the error if exception exists
                if ($exception) {
                    $logData = [
                        'requestURI' => $request->getRequestUri(),
                        'message' => $exception->getMessage(),
                        'code' => $exception->getCode(),
                        'file' => $exception->getFile(),
                        'line' => $exception->getLine(),
                        'stacktrace' => $exception->getTraceAsString(),
                    ];

                    // Use Symfony's built-in logger
                    error_log('Application error: '.json_encode($logData));
                }
                break;
        }

        // Handle JSON response
        if (strpos($acceptType, 'application/json') !== false) {
            return new JsonResponse([
                'errors' => [
                    [
                        'status' => $statusCode,
                        'title' => ucwords(strtolower($this->view->message)),
                    ],
                ],
            ], $statusCode);
        }

        // Handle HTML response
        try {
            $loggedUser = User::getLoggedUser();
        } catch (\Exception $e) {
            $loggedUser = null;
        }

        if ($loggedUser) {
            $this->view->loggedUser = $loggedUser;
            $this->view->accountId = $request->query->get('account_id');
        }

        $this->view->exception = $exception;
        $this->view->statusCode = $statusCode;

        // Sanitize request parameters
        $requestParams = [];
        foreach ($request->query->all() as $key => $value) {
            $newKey = htmlspecialchars($key, ENT_QUOTES, 'UTF-8');
            $newValue = is_array($value) ? $value : htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            $requestParams[$newKey] = $newValue;
        }
        $this->view->requestParams = $requestParams;

        $response = $this->render('error/error.html.twig', [
            'view' => $this->view,
            'statusCode' => $statusCode,
        ]);

        $response->setStatusCode($statusCode);

        return $response;
    }
}
