<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Booking;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiBookingController extends ApiBaseController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/bookings/{CONFIRMATION_CODE}
     * http://localhost:9019/api/bookings/{CONFIRMATION_CODE}
     */
    #[Route('/api/bookings/{confirmation_code}{slash}', name: 'api_bookings_show', requirements: ['slash' => '/?'], defaults: ['slash' => ''], methods: ['GET', 'POST', 'PUT'])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request);

        $confirmation_code = $request->get('confirmation_code');
        $booking = $this->validateAndGetBooking($confirmation_code);
        $facility_id = $request->get('facility_id');
        if ($facility_id && $booking->getFacilityId() != $facility_id) {
            throw new ApiException(ApiException::BAD_REQUEST, 'Booking does not belong to given facility');
        }

        $json_body = $request->getContent();
        if ($request->isMethod('GET')) {
            return $this->json(['data' => Booking::toArray($booking)]);
        }

        if ($request->isMethod('PUT')) {
            return $this->json(['data' => Booking::updateFromJson($booking, $json_body)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    public function validateAndGetBooking($confirmation_code)
    {
        try {
            $booking = Booking::validateConfirmationCode($confirmation_code);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }
        try {
            $facility = $booking->getFacility();
            User::validateFacilityAccess($facility);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED);
        }

        return $booking;
    }
}
